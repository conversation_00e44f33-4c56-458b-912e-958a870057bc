# TradingPanel 真实数据使用指南

## 概述

TradingPanel 现在已经支持使用真实的CTP接口数据，而不仅仅是模拟数据。本文档将指导您如何配置和使用真实数据功能。

## 功能特性

### ✅ 已实现的功能

1. **真实CTP连接**
   - 自动检测现有CTP连接
   - 支持MD（行情）和Trader（交易）API
   - 连接状态实时显示

2. **真实行情数据**
   - 订阅rb2509合约行情
   - 实时价格更新
   - 动态生成买卖盘档位

3. **真实交易功能**
   - 支持限价单下单
   - 支持撤单操作
   - 交易状态反馈

4. **智能数据源切换**
   - 真实数据优先
   - 自动降级到模拟数据
   - 手动切换数据源

5. **状态监控**
   - CTP连接状态显示
   - 数据源类型显示
   - 测试工具集成

## 使用步骤

### 1. 配置CTP服务器

首先确保您已经配置了正确的CTP服务器信息：

```typescript
// 在 src/config/ctpConfig.ts 中配置
const serverConfig = {
  name: '您的服务器名称',
  brokerId: '期货公司代码',
  tradeFront: 'tcp://交易服务器IP:端口',
  marketFront: 'tcp://行情服务器IP:端口',
  authCode: '认证码',
  userProductInfo: '产品信息',
  appId: '应用标识'
}
```

### 2. 登录系统

1. 打开应用，访问登录页面
2. 选择正确的服务器配置
3. 输入您的交易账号和密码
4. 点击"测试连接"验证配置
5. 点击"登录"建立CTP连接

### 3. 使用TradingPanel

1. 登录成功后，导航到交易面板页面
2. 检查左侧状态显示：
   - **行情**: 应显示"已登录"
   - **交易**: 应显示"已登录"  
   - **数据**: 应显示"真实"

### 4. 验证真实数据

您可以通过以下方式验证是否使用真实数据：

1. **价格变化**: 观察价格是否实时变化
2. **状态指示**: 左侧显示"数据: 真实"
3. **测试工具**: 点击"测试CTP"按钮运行诊断
4. **控制台日志**: 查看浏览器控制台的日志输出

## 界面说明

### 状态指示器

- **行情状态**: 显示行情API连接状态
- **交易状态**: 显示交易API连接状态
- **数据类型**: 显示当前使用的数据源（真实/模拟）

### 控制按钮

- **重连CTP**: 重新建立CTP连接
- **切换数据源**: 在真实数据和模拟数据之间切换
- **测试CTP**: 运行CTP连接诊断测试

### 交易操作

1. **下单**: 点击买单/卖单列进行下单
2. **撤单**: 点击撤单列撤销订单
3. **价格选择**: 点击价格列自动填入价格

## 故障排除

### 常见问题

1. **显示"模拟"数据**
   - 检查CTP服务器配置是否正确
   - 确认网络连接正常
   - 验证账号密码是否正确
   - 点击"重连CTP"重新尝试

2. **连接失败**
   - 检查服务器地址和端口
   - 确认认证码和应用标识
   - 查看控制台错误信息
   - 尝试使用"测试连接"功能

3. **行情数据不更新**
   - 检查行情API状态
   - 确认合约代码正确（rb2509）
   - 重新订阅行情数据

### 调试工具

使用内置的测试工具进行诊断：

```javascript
// 在浏览器控制台中运行
import { runTradingPanelTest } from './src/utils/tradingPanelTest'
await runTradingPanelTest()
```

## 安全注意事项

⚠️ **重要提醒**

1. **测试环境**: 建议先在模拟环境中测试
2. **小额交易**: 真实环境请使用小额资金测试
3. **风险控制**: 设置合理的止损和仓位控制
4. **网络稳定**: 确保网络连接稳定可靠

## 技术架构

### 数据流

```
用户操作 → TradingPanel → CtpService → Tauri后端 → CTP原生库 → 期货交易所
```

### 关键组件

- **TradingPanel.vue**: 主交易界面
- **CtpService.ts**: CTP服务封装
- **ctpConfig.ts**: 服务器配置管理
- **tradingPanelTest.ts**: 测试工具

## 更新日志

### v1.0.0 (当前版本)

- ✅ 集成真实CTP接口
- ✅ 实现行情数据订阅
- ✅ 支持真实下单和撤单
- ✅ 添加状态监控和测试工具
- ✅ 智能数据源切换

### 计划功能

- 🔄 持仓查询和显示
- 🔄 账户资金查询
- 🔄 订单历史查询
- 🔄 多合约支持
- 🔄 高级订单类型

## 支持

如果您在使用过程中遇到问题，请：

1. 查看控制台日志
2. 运行CTP测试工具
3. 检查网络和配置
4. 联系技术支持

---

**注意**: 本系统仅供学习和测试使用，实际交易请谨慎操作并承担相应风险。
