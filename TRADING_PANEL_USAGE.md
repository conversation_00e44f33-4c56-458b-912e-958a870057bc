# 交易面板使用说明

## 界面布局

交易面板采用左右分栏布局：

### 左侧操作列
- **合约信息**: 显示当前合约名称(rb2509)和缩放控制
- **市场数据**: 实时显示时间、价格变化、成交量、持仓量等
- **下单控制**: 
  - 数量和价格输入框
  - Order(A)/Order(B) 模式选择
  - 各种交易选项(全手指、CLimit等)
- **持仓信息**: 显示净仓、C/T持仓和盈亏
- **操作说明**: 简要说明各列功能

### 右侧五列表格
1. **第一列 - 撤单**: 点击"×"可快速撤单
2. **第二列 - 买单**: 显示买单数量，点击可按A/B模式下买单
3. **第三列 - 价位**: 显示价格，中间黄色行为当前价格
4. **第四列 - 卖单**: 显示卖单数量，点击可按A/B模式下卖单  
5. **第五列 - 空列**: 预留列，暂无功能

## 功能特点

### 快速交易
- **一键撤单**: 点击第一列的"×"按钮即可撤单
- **快速下单**: 点击第二列(买单)或第四列(卖单)可快速下单
- **价格填入**: 点击价位会自动填入下单价格

### A/B模式
- **Order(A)**: 默认A模式状态
- **Order(B)**: 默认B模式状态，后期制作需与撤单限制功能对换位置

### 实时数据
- 价格和数量实时更新
- 颜色区分：红色为卖盘，蓝色为买盘
- 当前价格以黄色背景突出显示

### 快捷键支持
- **+/-**: 放大/缩小字体
- **Enter**: 下单
- **Delete**: 撤单
- **Esc**: 清除选择
- **P**: 切换开仓/平仓模式
- **K**: 当前合约开仓模式

## 使用流程

1. **查看行情**: 观察右侧表格中的价位和挂单情况
2. **设置参数**: 在左侧设置下单数量、选择A/B模式
3. **快速交易**: 
   - 点击买单列进行买入
   - 点击卖单列进行卖出
   - 点击撤单列取消订单
4. **监控持仓**: 查看左侧的持仓信息和盈亏状况

## 注意事项

- 撤单限制功能：可设置最大撤单数量限制
- 开仓平仓模式：可选择仅开仓或仅平仓模式
- 实时数据更新：界面会自动更新市场数据
- 响应式设计：支持缩放和字体大小调整

## 技术实现

- 基于Vue 3 + TypeScript开发
- 使用Grid布局实现五列表格
- 支持实时数据更新和用户交互
- 响应式设计，适配不同屏幕尺寸
