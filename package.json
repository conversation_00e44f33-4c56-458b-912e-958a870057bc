{"name": "tauri_app_vue", "private": true, "version": "0.1.0", "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc --noEmit && vite build", "preview": "vite preview", "tauri": "tauri"}, "dependencies": {"@babel/core": "^7.28.0", "@tauri-apps/plugin-opener": "^2", "ant-design-vue": "^4.2.6", "html2canvas": "^1.4.1", "vue": "^3.5.13", "vue-router": "^4.5.1"}, "devDependencies": {"@tauri-apps/api": "^2.7.0", "@tauri-apps/cli": "^2.7.1", "@types/node": "^24.0.12", "@vitejs/plugin-vue": "^5.2.1", "@vue/compiler-sfc": "^3.5.17", "less": "^4.3.0", "rollup-plugin-visualizer": "^5.12.0", "typescript": "~5.6.2", "vite": "^6.0.3", "vite-plugin-compression": "^0.5.1", "vite-plugin-vue-devtools": "^7.7.5", "vue-tsc": "^2.2.12"}}