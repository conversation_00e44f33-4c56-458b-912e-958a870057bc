[package]
name = "tauri_app_vue"
version = "0.1.0"
description = "A Tauri App"
authors = ["you"]
edition = "2021"
build = "build.rs"

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[lib]
# The `_lib` suffix may seem redundant but it is necessary
# to make the lib name unique and wouldn't conflict with the bin name.
# This seems to be only an issue on Windows, see https://github.com/rust-lang/cargo/issues/8519
name = "tauri_app_vue_lib"
crate-type = ["staticlib", "cdylib", "rlib"]

[build-dependencies]
tauri-build = { version = "2", features = [] }
bindgen = "0.69" # FFI rust-bindgen
regex = "1.4.1"
copy_to_output = "2.0.0"
clang-sys = { version = "1", features = ["clang_6_0", "runtime"] }
clang = "2.0.0"
encoding = "0.2.33"
Inflector = "0.11.4"
libc = { version = "0.2.39", default-features = false }
# winres = "0.1"  # 注释掉，因为与 tauri_build 冲突

[dependencies]
tauri = { version = "2", features = [] }
tauri-plugin-opener = "2"
serde = { version = "1", features = ["derive"] }
serde_json = "1"
tokio = { version = "1", features = ["full"] }
anyhow = "1"
env_logger = "0.8.1"
chrono = "0.4.19"
encoding = "0.2.33"
memchr = "2.3.3"
simple-error = "0.2.1"
time = "0.1.43"
serde_derive = "1.0.117"
futures = "0.3.5"
bincode = { version = "2.0.0-rc.2", features = ["derive"] }
lazy_static = "1.4"

# [dev-dependencies]
tracing = "0.1.34"
tracing-subscriber = { version = "0.3.11", features = ["env-filter"] }
