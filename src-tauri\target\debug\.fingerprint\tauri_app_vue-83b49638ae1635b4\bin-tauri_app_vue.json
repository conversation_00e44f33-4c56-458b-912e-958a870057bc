{"rustc": 16591470773350601817, "features": "[]", "declared_features": "[]", "target": 5081230978893891133, "profile": 8731458305071235362, "path": 4942398508502643691, "deps": [[1556188385098558368, "simple_error", false, 14813109379531436022], [2706460456408817945, "futures", false, 15644774609817167760], [5746673292440776345, "env_logger", false, 17346411697804912623], [8606274917505247608, "tracing", false, 17693373848412442596], [9689903380558560274, "serde", false, 9185589243144461776], [9897246384292347999, "chrono", false, 760621913227717554], [11862723254873017773, "bincode", false, 439598873087682109], [12393800526703971956, "tokio", false, 4701544135245576895], [13625485746686963219, "anyhow", false, 1167168686564364804], [14039947826026167952, "tauri", false, 16236185364515443998], [15180317924524316486, "tauri_app_vue_lib", false, 13581446625395455172], [15180317924524316486, "build_script_build", false, 11475581059304386789], [15367738274754116744, "serde_json", false, 687319917565722559], [15932120279885307830, "memchr", false, 1947052073502873835], [16230660778393187092, "tracing_subscriber", false, 1573258105619610402], [16257276029081467297, "serde_derive", false, 4515218308895454717], [16702348383442838006, "tauri_plugin_opener", false, 16697723794441504388], [17675327481376616781, "encoding", false, 694599585179085979], [17874132307072864906, "time", false, 6021564073651230096], [17917672826516349275, "lazy_static", false, 3337408258675683795]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri_app_vue-83b49638ae1635b4\\dep-bin-tauri_app_vue", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}