D:\personalWork\tauri\demo2\tauri_app_vue\src-tauri\target\debug\deps\libtauri_app_vue-315644e305875ee7.rmeta: src\main.rs src\file_io.rs src\ctp_commands.rs D:\personalWork\tauri\demo2\tauri_app_vue\src-tauri\target\debug\build\tauri_app_vue-5ed9c6ace4771e4d\out/524e70828b177840a8abc536a997eb4579ce81975771e153329f0de43810a5a7

D:\personalWork\tauri\demo2\tauri_app_vue\src-tauri\target\debug\deps\tauri_app_vue-315644e305875ee7.d: src\main.rs src\file_io.rs src\ctp_commands.rs D:\personalWork\tauri\demo2\tauri_app_vue\src-tauri\target\debug\build\tauri_app_vue-5ed9c6ace4771e4d\out/524e70828b177840a8abc536a997eb4579ce81975771e153329f0de43810a5a7

src\main.rs:
src\file_io.rs:
src\ctp_commands.rs:
D:\personalWork\tauri\demo2\tauri_app_vue\src-tauri\target\debug\build\tauri_app_vue-5ed9c6ace4771e4d\out/524e70828b177840a8abc536a997eb4579ce81975771e153329f0de43810a5a7:

# env-dep:CARGO_PKG_AUTHORS=you
# env-dep:CARGO_PKG_DESCRIPTION=A Tauri App
# env-dep:CARGO_PKG_NAME=tauri_app_vue
# env-dep:OUT_DIR=D:\\personalWork\\tauri\\demo2\\tauri_app_vue\\src-tauri\\target\\debug\\build\\tauri_app_vue-5ed9c6ace4771e4d\\out
