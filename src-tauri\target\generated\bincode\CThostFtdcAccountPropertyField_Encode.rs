impl :: bincode :: Encode for CThostFtdcAccountPropertyField {fn encode < __E : :: bincode :: enc :: Encoder > (& self , encoder : & mut __E) ->core :: result :: Result < () , :: bincode :: error :: EncodeError > {:: bincode :: Encode :: encode (& self . BrokerID , encoder) ?; :: bincode :: Encode :: encode (& self . AccountID , encoder) ?; :: bincode :: Encode :: encode (& self . BankID , encoder) ?; :: bincode :: Encode :: encode (& self . BankAccount , encoder) ?; :: bincode :: Encode :: encode (& self . OpenName , encoder) ?; :: bincode :: Encode :: encode (& self . OpenBank , encoder) ?; :: bincode :: Encode :: encode (& self . IsActive , encoder) ?; :: bincode :: Encode :: encode (& self . AccountSourceType , encoder) ?; :: bincode :: Encode :: encode (& self . OpenDate , encoder) ?; :: bincode :: Encode :: encode (& self . CancelDate , encoder) ?; :: bincode :: Encode :: encode (& self . OperatorID , encoder) ?; :: bincode :: Encode :: encode (& self . OperateDate , encoder) ?; :: bincode :: Encode :: encode (& self . OperateTime , encoder) ?; :: bincode :: Encode :: encode (& self . CurrencyID , encoder) ?; core :: result :: Result :: Ok (())}}