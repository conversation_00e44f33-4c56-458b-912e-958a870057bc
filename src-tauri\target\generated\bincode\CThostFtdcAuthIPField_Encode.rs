impl :: bincode :: Encode for CThostFtdcAuthIPField {fn encode < __E : :: bincode :: enc :: Encoder > (& self , encoder : & mut __E) ->core :: result :: Result < () , :: bincode :: error :: EncodeError > {:: bincode :: Encode :: encode (& self . BrokerID , encoder) ?; :: bincode :: Encode :: encode (& self . AppID , encoder) ?; :: bincode :: Encode :: encode (& self . IPAddress , encoder) ?; core :: result :: Result :: Ok (())}}