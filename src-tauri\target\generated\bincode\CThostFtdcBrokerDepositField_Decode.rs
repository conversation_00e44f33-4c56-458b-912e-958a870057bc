impl < __Context > :: bincode :: Decode < __Context > for CThostFtdcBrokerDepositField {fn decode < __D : :: bincode :: de :: Decoder < Context = __Context > > (decoder : & mut __D) ->core :: result :: Result < Self , :: bincode :: error :: DecodeError > {core :: result :: Result :: Ok (Self {TradingDay : :: bincode :: Decode :: decode (decoder) ?, BrokerID : :: bincode :: Decode :: decode (decoder) ?, ParticipantID : :: bincode :: Decode :: decode (decoder) ?, ExchangeID : :: bincode :: Decode :: decode (decoder) ?, PreBalance : :: bincode :: Decode :: decode (decoder) ?, CurrMargin : :: bincode :: Decode :: decode (decoder) ?, CloseProfit : :: bincode :: Decode :: decode (decoder) ?, Balance : :: bincode :: Decode :: decode (decoder) ?, Deposit : :: bincode :: Decode :: decode (decoder) ?, Withdraw : :: bincode :: Decode :: decode (decoder) ?, Available : :: bincode :: Decode :: decode (decoder) ?, Reserve : :: bincode :: Decode :: decode (decoder) ?, FrozenMargin : :: bincode :: Decode :: decode (decoder) ?,})}} impl < '__de , __Context > :: bincode :: BorrowDecode < '__de , __Context > for CThostFtdcBrokerDepositField {fn borrow_decode < __D : :: bincode :: de :: BorrowDecoder <'__de , Context = __Context > > (decoder : & mut __D) ->core :: result :: Result < Self , :: bincode :: error :: DecodeError > {core :: result :: Result :: Ok (Self {TradingDay : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, BrokerID : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, ParticipantID : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, ExchangeID : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, PreBalance : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, CurrMargin : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, CloseProfit : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, Balance : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, Deposit : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, Withdraw : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, Available : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, Reserve : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, FrozenMargin : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?,})}}