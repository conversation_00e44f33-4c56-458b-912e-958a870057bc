impl :: bincode :: Encode for CThostFtdcBrokerDepositField {fn encode < __E : :: bincode :: enc :: Encoder > (& self , encoder : & mut __E) ->core :: result :: Result < () , :: bincode :: error :: EncodeError > {:: bincode :: Encode :: encode (& self . TradingDay , encoder) ?; :: bincode :: Encode :: encode (& self . BrokerID , encoder) ?; :: bincode :: Encode :: encode (& self . ParticipantID , encoder) ?; :: bincode :: Encode :: encode (& self . ExchangeID , encoder) ?; :: bincode :: Encode :: encode (& self . PreBalance , encoder) ?; :: bincode :: Encode :: encode (& self . CurrMargin , encoder) ?; :: bincode :: Encode :: encode (& self . CloseProfit , encoder) ?; :: bincode :: Encode :: encode (& self . Balance , encoder) ?; :: bincode :: Encode :: encode (& self . Deposit , encoder) ?; :: bincode :: Encode :: encode (& self . Withdraw , encoder) ?; :: bincode :: Encode :: encode (& self . Available , encoder) ?; :: bincode :: Encode :: encode (& self . Reserve , encoder) ?; :: bincode :: Encode :: encode (& self . FrozenMargin , encoder) ?; core :: result :: Result :: Ok (())}}