impl :: bincode :: Encode for CThostFtdcBrokerUserEventField {fn encode < __E : :: bincode :: enc :: Encoder > (& self , encoder : & mut __E) ->core :: result :: Result < () , :: bincode :: error :: EncodeError > {:: bincode :: Encode :: encode (& self . BrokerID , encoder) ?; :: bincode :: Encode :: encode (& self . UserID , encoder) ?; :: bincode :: Encode :: encode (& self . UserEventType , encoder) ?; :: bincode :: Encode :: encode (& self . EventSequenceNo , encoder) ?; :: bincode :: Encode :: encode (& self . EventDate , encoder) ?; :: bincode :: Encode :: encode (& self . EventTime , encoder) ?; :: bincode :: Encode :: encode (& self . UserEventInfo , encoder) ?; :: bincode :: Encode :: encode (& self . InvestorID , encoder) ?; :: bincode :: Encode :: encode (& self . reserve1 , encoder) ?; :: bincode :: Encode :: encode (& self . InstrumentID , encoder) ?; core :: result :: Result :: Ok (())}}