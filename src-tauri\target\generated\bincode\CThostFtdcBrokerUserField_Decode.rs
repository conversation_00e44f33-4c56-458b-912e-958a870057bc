impl < __Context > :: bincode :: Decode < __Context > for CThostFtdcBrokerUserField {fn decode < __D : :: bincode :: de :: Decoder < Context = __Context > > (decoder : & mut __D) ->core :: result :: Result < Self , :: bincode :: error :: DecodeError > {core :: result :: Result :: Ok (Self {BrokerID : :: bincode :: Decode :: decode (decoder) ?, UserID : :: bincode :: Decode :: decode (decoder) ?, UserName : :: bincode :: Decode :: decode (decoder) ?, UserType : :: bincode :: Decode :: decode (decoder) ?, IsActive : :: bincode :: Decode :: decode (decoder) ?, IsUsingOTP : :: bincode :: Decode :: decode (decoder) ?, IsAuthForce : :: bincode :: Decode :: decode (decoder) ?,})}} impl < '__de , __Context > :: bincode :: BorrowDecode < '__de , __Context > for CThostFtdcBrokerUserField {fn borrow_decode < __D : :: bincode :: de :: BorrowDecoder <'__de , Context = __Context > > (decoder : & mut __D) ->core :: result :: Result < Self , :: bincode :: error :: DecodeError > {core :: result :: Result :: Ok (Self {BrokerID : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, UserID : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, UserName : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, UserType : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, IsActive : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, IsUsingOTP : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, IsAuthForce : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?,})}}