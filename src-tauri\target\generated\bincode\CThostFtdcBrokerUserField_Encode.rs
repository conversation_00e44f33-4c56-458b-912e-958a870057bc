impl :: bincode :: Encode for CThostFtdcBrokerUserField {fn encode < __E : :: bincode :: enc :: Encoder > (& self , encoder : & mut __E) ->core :: result :: Result < () , :: bincode :: error :: EncodeError > {:: bincode :: Encode :: encode (& self . BrokerID , encoder) ?; :: bincode :: Encode :: encode (& self . UserID , encoder) ?; :: bincode :: Encode :: encode (& self . UserName , encoder) ?; :: bincode :: Encode :: encode (& self . UserType , encoder) ?; :: bincode :: Encode :: encode (& self . IsActive , encoder) ?; :: bincode :: Encode :: encode (& self . IsUsingOTP , encoder) ?; :: bincode :: Encode :: encode (& self . IsAuthF<PERSON><PERSON> , encoder) ?; core :: result :: Result :: Ok (())}}