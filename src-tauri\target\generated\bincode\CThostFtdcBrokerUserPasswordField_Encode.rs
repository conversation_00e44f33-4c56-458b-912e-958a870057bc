impl :: bincode :: Encode for CThostFtdcBrokerUserPasswordField {fn encode < __E : :: bincode :: enc :: Encoder > (& self , encoder : & mut __E) ->core :: result :: Result < () , :: bincode :: error :: EncodeError > {:: bincode :: Encode :: encode (& self . BrokerID , encoder) ?; :: bincode :: Encode :: encode (& self . UserID , encoder) ?; :: bincode :: Encode :: encode (& self . Password , encoder) ?; :: bincode :: Encode :: encode (& self . LastUpdateTime , encoder) ?; :: bincode :: Encode :: encode (& self . LastLoginTime , encoder) ?; :: bincode :: Encode :: encode (& self . ExpireDate , encoder) ?; :: bincode :: Encode :: encode (& self . WeakExpireDate , encoder) ?; core :: result :: Result :: Ok (())}}