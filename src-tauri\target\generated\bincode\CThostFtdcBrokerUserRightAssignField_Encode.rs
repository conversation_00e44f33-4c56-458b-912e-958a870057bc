impl :: bincode :: Encode for CThostFtdcBrokerUserRightAssignField {fn encode < __E : :: bincode :: enc :: Encoder > (& self , encoder : & mut __E) ->core :: result :: Result < () , :: bincode :: error :: EncodeError > {:: bincode :: Encode :: encode (& self . BrokerID , encoder) ?; :: bincode :: Encode :: encode (& self . DRIdentityID , encoder) ?; :: bincode :: Encode :: encode (& self . Tradeable , encoder) ?; core :: result :: Result :: Ok (())}}