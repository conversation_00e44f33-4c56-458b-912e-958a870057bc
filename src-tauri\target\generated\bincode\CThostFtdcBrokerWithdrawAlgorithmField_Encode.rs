impl :: bincode :: Encode for CThostFtdcBrokerWithdrawAlgorithmField {fn encode < __E : :: bincode :: enc :: Encoder > (& self , encoder : & mut __E) ->core :: result :: Result < () , :: bincode :: error :: EncodeError > {:: bincode :: Encode :: encode (& self . BrokerID , encoder) ?; :: bincode :: Encode :: encode (& self . WithdrawAlgorithm , encoder) ?; :: bincode :: Encode :: encode (& self . UsingRatio , encoder) ?; :: bincode :: Encode :: encode (& self . IncludeCloseProfit , encoder) ?; :: bincode :: Encode :: encode (& self . AllWithoutTrade , encoder) ?; :: bincode :: Encode :: encode (& self . AvailIncludeCloseProfit , encoder) ?; :: bincode :: Encode :: encode (& self . IsBrokerUserEvent , encoder) ?; :: bincode :: Encode :: encode (& self . CurrencyID , encoder) ?; :: bincode :: Encode :: encode (& self . FundMortgageRatio , encoder) ?; :: bincode :: Encode :: encode (& self . BalanceAlgorithm , encoder) ?; core :: result :: Result :: Ok (())}}