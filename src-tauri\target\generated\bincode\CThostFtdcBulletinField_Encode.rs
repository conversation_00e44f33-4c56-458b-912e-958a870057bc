impl :: bincode :: Encode for CThostFtdcBulletinField {fn encode < __E : :: bincode :: enc :: Encoder > (& self , encoder : & mut __E) ->core :: result :: Result < () , :: bincode :: error :: EncodeError > {:: bincode :: Encode :: encode (& self . ExchangeID , encoder) ?; :: bincode :: Encode :: encode (& self . TradingDay , encoder) ?; :: bincode :: Encode :: encode (& self . BulletinID , encoder) ?; :: bincode :: Encode :: encode (& self . SequenceNo , encoder) ?; :: bincode :: Encode :: encode (& self . NewsType , encoder) ?; :: bincode :: Encode :: encode (& self . NewsUrgency , encoder) ?; :: bincode :: Encode :: encode (& self . SendTime , encoder) ?; :: bincode :: Encode :: encode (& self . Abstract , encoder) ?; :: bincode :: Encode :: encode (& self . <PERSON><PERSON><PERSON> , encoder) ?; :: bincode :: Encode :: encode (& self . Content , encoder) ?; :: bincode :: Encode :: encode (& self . URLLink , encoder) ?; :: bincode :: Encode :: encode (& self . MarketID , encoder) ?; core :: result :: Result :: Ok (())}}