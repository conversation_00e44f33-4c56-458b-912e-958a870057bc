impl < __Context > :: bincode :: Decode < __Context > for CThostFtdcChangeAccountField {fn decode < __D : :: bincode :: de :: Decoder < Context = __Context > > (decoder : & mut __D) ->core :: result :: Result < Self , :: bincode :: error :: DecodeError > {core :: result :: Result :: Ok (Self {TradeCode : :: bincode :: Decode :: decode (decoder) ?, BankID : :: bincode :: Decode :: decode (decoder) ?, BankBranchID : :: bincode :: Decode :: decode (decoder) ?, BrokerID : :: bincode :: Decode :: decode (decoder) ?, BrokerBranchID : :: bincode :: Decode :: decode (decoder) ?, TradeDate : :: bincode :: Decode :: decode (decoder) ?, TradeTime : :: bincode :: Decode :: decode (decoder) ?, BankSerial : :: bincode :: Decode :: decode (decoder) ?, TradingDay : :: bincode :: Decode :: decode (decoder) ?, PlateSerial : :: bincode :: Decode :: decode (decoder) ?, LastFragment : :: bincode :: Decode :: decode (decoder) ?, SessionID : :: bincode :: Decode :: decode (decoder) ?, CustomerName : :: bincode :: Decode :: decode (decoder) ?, IdCardType : :: bincode :: Decode :: decode (decoder) ?, IdentifiedCardNo : :: bincode :: Decode :: decode (decoder) ?, Gender : :: bincode :: Decode :: decode (decoder) ?, CountryCode : :: bincode :: Decode :: decode (decoder) ?, CustType : :: bincode :: Decode :: decode (decoder) ?, Address : :: bincode :: Decode :: decode (decoder) ?, ZipCode : :: bincode :: Decode :: decode (decoder) ?, Telephone : :: bincode :: Decode :: decode (decoder) ?, MobilePhone : :: bincode :: Decode :: decode (decoder) ?, Fax : :: bincode :: Decode :: decode (decoder) ?, EMail : :: bincode :: Decode :: decode (decoder) ?, MoneyAccountStatus : :: bincode :: Decode :: decode (decoder) ?, BankAccount : :: bincode :: Decode :: decode (decoder) ?, BankPassWord : :: bincode :: Decode :: decode (decoder) ?, NewBankAccount : :: bincode :: Decode :: decode (decoder) ?, NewBankPassWord : :: bincode :: Decode :: decode (decoder) ?, AccountID : :: bincode :: Decode :: decode (decoder) ?, Password : :: bincode :: Decode :: decode (decoder) ?, BankAccType : :: bincode :: Decode :: decode (decoder) ?, InstallID : :: bincode :: Decode :: decode (decoder) ?, VerifyCertNoFlag : :: bincode :: Decode :: decode (decoder) ?, CurrencyID : :: bincode :: Decode :: decode (decoder) ?, BrokerIDByBank : :: bincode :: Decode :: decode (decoder) ?, BankPwdFlag : :: bincode :: Decode :: decode (decoder) ?, SecuPwdFlag : :: bincode :: Decode :: decode (decoder) ?, TID : :: bincode :: Decode :: decode (decoder) ?, Digest : :: bincode :: Decode :: decode (decoder) ?, ErrorID : :: bincode :: Decode :: decode (decoder) ?, ErrorMsg : :: bincode :: Decode :: decode (decoder) ?, LongCustomerName : :: bincode :: Decode :: decode (decoder) ?,})}} impl < '__de , __Context > :: bincode :: BorrowDecode < '__de , __Context > for CThostFtdcChangeAccountField {fn borrow_decode < __D : :: bincode :: de :: BorrowDecoder <'__de , Context = __Context > > (decoder : & mut __D) ->core :: result :: Result < Self , :: bincode :: error :: DecodeError > {core :: result :: Result :: Ok (Self {TradeCode : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, BankID : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, BankBranchID : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, BrokerID : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, BrokerBranchID : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, TradeDate : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, TradeTime : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, BankSerial : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, TradingDay : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, PlateSerial : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, LastFragment : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, SessionID : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, CustomerName : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, IdCardType : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, IdentifiedCardNo : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, Gender : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, CountryCode : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, CustType : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, Address : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, ZipCode : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, Telephone : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, MobilePhone : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, Fax : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, EMail : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, MoneyAccountStatus : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, BankAccount : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, BankPassWord : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, NewBankAccount : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, NewBankPassWord : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, AccountID : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, Password : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, BankAccType : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, InstallID : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, VerifyCertNoFlag : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, CurrencyID : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, BrokerIDByBank : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, BankPwdFlag : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, SecuPwdFlag : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, TID : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, Digest : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, ErrorID : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, ErrorMsg : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, LongCustomerName : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?,})}}