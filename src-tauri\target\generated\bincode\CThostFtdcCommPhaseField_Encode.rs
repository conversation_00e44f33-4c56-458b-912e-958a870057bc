impl :: bincode :: Encode for CThostFtdcCommPhaseField {fn encode < __E : :: bincode :: enc :: Encoder > (& self , encoder : & mut __E) ->core :: result :: Result < () , :: bincode :: error :: EncodeError > {:: bincode :: Encode :: encode (& self . TradingDay , encoder) ?; :: bincode :: Encode :: encode (& self . CommPhaseNo , encoder) ?; :: bincode :: Encode :: encode (& self . SystemID , encoder) ?; core :: result :: Result :: Ok (())}}