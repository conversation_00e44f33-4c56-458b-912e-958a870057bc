impl :: bincode :: Encode for CThostFtdcCommRateModelField {fn encode < __E : :: bincode :: enc :: Encoder > (& self , encoder : & mut __E) ->core :: result :: Result < () , :: bincode :: error :: EncodeError > {:: bincode :: Encode :: encode (& self . BrokerID , encoder) ?; :: bincode :: Encode :: encode (& self . CommModelID , encoder) ?; :: bincode :: Encode :: encode (& self . CommModelName , encoder) ?; core :: result :: Result :: Ok (())}}