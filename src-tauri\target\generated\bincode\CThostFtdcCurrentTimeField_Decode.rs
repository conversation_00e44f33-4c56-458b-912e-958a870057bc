impl < __Context > :: bincode :: Decode < __Context > for CThostFtdcCurrentTimeField {fn decode < __D : :: bincode :: de :: Decoder < Context = __Context > > (decoder : & mut __D) ->core :: result :: Result < Self , :: bincode :: error :: DecodeError > {core :: result :: Result :: Ok (Self {CurrDate : :: bincode :: Decode :: decode (decoder) ?, CurrTime : :: bincode :: Decode :: decode (decoder) ?, CurrMillisec : :: bincode :: Decode :: decode (decoder) ?, ActionDay : :: bincode :: Decode :: decode (decoder) ?,})}} impl < '__de , __Context > :: bincode :: BorrowDecode < '__de , __Context > for CThostFtdcCurrentTimeField {fn borrow_decode < __D : :: bincode :: de :: BorrowDecoder <'__de , Context = __Context > > (decoder : & mut __D) ->core :: result :: Result < Self , :: bincode :: error :: DecodeError > {core :: result :: Result :: Ok (Self {CurrDate : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, CurrTime : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, CurrMillisec : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, ActionDay : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?,})}}