impl :: bincode :: Encode for CThostFtdcDRTransferField {fn encode < __E : :: bincode :: enc :: Encoder > (& self , encoder : & mut __E) ->core :: result :: Result < () , :: bincode :: error :: EncodeError > {:: bincode :: Encode :: encode (& self . OrigDRIdentityID , encoder) ?; :: bincode :: Encode :: encode (& self . DestDRIdentityID , encoder) ?; :: bincode :: Encode :: encode (& self . OrigBrokerID , encoder) ?; :: bincode :: Encode :: encode (& self . DestBrokerID , encoder) ?; core :: result :: Result :: Ok (())}}