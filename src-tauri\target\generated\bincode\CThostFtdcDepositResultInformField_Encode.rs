impl :: bincode :: Encode for CThostFtdcDepositResultInformField {fn encode < __E : :: bincode :: enc :: Encoder > (& self , encoder : & mut __E) ->core :: result :: Result < () , :: bincode :: error :: EncodeError > {:: bincode :: Encode :: encode (& self . DepositSeqNo , encoder) ?; :: bincode :: Encode :: encode (& self . BrokerID , encoder) ?; :: bincode :: Encode :: encode (& self . InvestorID , encoder) ?; :: bincode :: Encode :: encode (& self . Deposit , encoder) ?; :: bincode :: Encode :: encode (& self . RequestID , encoder) ?; :: bincode :: Encode :: encode (& self . ReturnCode , encoder) ?; :: bincode :: Encode :: encode (& self . DescrInfoForReturnCode , encoder) ?; core :: result :: Result :: Ok (())}}