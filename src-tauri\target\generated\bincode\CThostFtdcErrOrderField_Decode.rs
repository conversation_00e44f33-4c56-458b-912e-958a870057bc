impl < __Context > :: bincode :: Decode < __Context > for CThostFtdcErrOrderField {fn decode < __D : :: bincode :: de :: Decoder < Context = __Context > > (decoder : & mut __D) ->core :: result :: Result < Self , :: bincode :: error :: DecodeError > {core :: result :: Result :: Ok (Self {BrokerID : :: bincode :: Decode :: decode (decoder) ?, InvestorID : :: bincode :: Decode :: decode (decoder) ?, reserve1 : :: bincode :: Decode :: decode (decoder) ?, OrderRef : :: bincode :: Decode :: decode (decoder) ?, UserID : :: bincode :: Decode :: decode (decoder) ?, OrderPriceType : :: bincode :: Decode :: decode (decoder) ?, Direction : :: bincode :: Decode :: decode (decoder) ?, CombOffsetFlag : :: bincode :: Decode :: decode (decoder) ?, CombHedgeFlag : :: bincode :: Decode :: decode (decoder) ?, LimitPrice : :: bincode :: Decode :: decode (decoder) ?, VolumeTotalOriginal : :: bincode :: Decode :: decode (decoder) ?, TimeCondition : :: bincode :: Decode :: decode (decoder) ?, GTDDate : :: bincode :: Decode :: decode (decoder) ?, VolumeCondition : :: bincode :: Decode :: decode (decoder) ?, MinVolume : :: bincode :: Decode :: decode (decoder) ?, ContingentCondition : :: bincode :: Decode :: decode (decoder) ?, StopPrice : :: bincode :: Decode :: decode (decoder) ?, ForceCloseReason : :: bincode :: Decode :: decode (decoder) ?, IsAutoSuspend : :: bincode :: Decode :: decode (decoder) ?, BusinessUnit : :: bincode :: Decode :: decode (decoder) ?, RequestID : :: bincode :: Decode :: decode (decoder) ?, UserForceClose : :: bincode :: Decode :: decode (decoder) ?, ErrorID : :: bincode :: Decode :: decode (decoder) ?, ErrorMsg : :: bincode :: Decode :: decode (decoder) ?, IsSwapOrder : :: bincode :: Decode :: decode (decoder) ?, ExchangeID : :: bincode :: Decode :: decode (decoder) ?, InvestUnitID : :: bincode :: Decode :: decode (decoder) ?, AccountID : :: bincode :: Decode :: decode (decoder) ?, CurrencyID : :: bincode :: Decode :: decode (decoder) ?, ClientID : :: bincode :: Decode :: decode (decoder) ?, reserve2 : :: bincode :: Decode :: decode (decoder) ?, MacAddress : :: bincode :: Decode :: decode (decoder) ?, InstrumentID : :: bincode :: Decode :: decode (decoder) ?, IPAddress : :: bincode :: Decode :: decode (decoder) ?,})}} impl < '__de , __Context > :: bincode :: BorrowDecode < '__de , __Context > for CThostFtdcErrOrderField {fn borrow_decode < __D : :: bincode :: de :: BorrowDecoder <'__de , Context = __Context > > (decoder : & mut __D) ->core :: result :: Result < Self , :: bincode :: error :: DecodeError > {core :: result :: Result :: Ok (Self {BrokerID : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, InvestorID : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, reserve1 : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, OrderRef : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, UserID : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, OrderPriceType : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, Direction : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, CombOffsetFlag : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, CombHedgeFlag : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, LimitPrice : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, VolumeTotalOriginal : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, TimeCondition : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, GTDDate : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, VolumeCondition : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, MinVolume : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, ContingentCondition : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, StopPrice : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, ForceCloseReason : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, IsAutoSuspend : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, BusinessUnit : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, RequestID : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, UserForceClose : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, ErrorID : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, ErrorMsg : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, IsSwapOrder : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, ExchangeID : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, InvestUnitID : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, AccountID : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, CurrencyID : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, ClientID : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, reserve2 : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, MacAddress : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, InstrumentID : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, IPAddress : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?,})}}