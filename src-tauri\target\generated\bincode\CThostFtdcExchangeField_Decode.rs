impl < __Context > :: bincode :: Decode < __Context > for CThostFtdcExchangeField {fn decode < __D : :: bincode :: de :: Decoder < Context = __Context > > (decoder : & mut __D) ->core :: result :: Result < Self , :: bincode :: error :: DecodeError > {core :: result :: Result :: Ok (Self {ExchangeID : :: bincode :: Decode :: decode (decoder) ?, ExchangeName : :: bincode :: Decode :: decode (decoder) ?, ExchangeProperty : :: bincode :: Decode :: decode (decoder) ?,})}} impl < '__de , __Context > :: bincode :: BorrowDecode < '__de , __Context > for CThostFtdcExchangeField {fn borrow_decode < __D : :: bincode :: de :: BorrowDecoder <'__de , Context = __Context > > (decoder : & mut __D) ->core :: result :: Result < Self , :: bincode :: error :: DecodeError > {core :: result :: Result :: Ok (Self {ExchangeID : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, ExchangeName : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, ExchangeProperty : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?,})}}