impl < __Context > :: bincode :: Decode < __Context > for CThostFtdcExchangeForQuoteField {fn decode < __D : :: bincode :: de :: Decoder < Context = __Context > > (decoder : & mut __D) ->core :: result :: Result < Self , :: bincode :: error :: DecodeError > {core :: result :: Result :: Ok (Self {ForQuoteLocalID : :: bincode :: Decode :: decode (decoder) ?, ExchangeID : :: bincode :: Decode :: decode (decoder) ?, ParticipantID : :: bincode :: Decode :: decode (decoder) ?, ClientID : :: bincode :: Decode :: decode (decoder) ?, reserve1 : :: bincode :: Decode :: decode (decoder) ?, TraderID : :: bincode :: Decode :: decode (decoder) ?, InstallID : :: bincode :: Decode :: decode (decoder) ?, InsertDate : :: bincode :: Decode :: decode (decoder) ?, InsertTime : :: bincode :: Decode :: decode (decoder) ?, ForQuoteStatus : :: bincode :: Decode :: decode (decoder) ?, reserve2 : :: bincode :: Decode :: decode (decoder) ?, MacAddress : :: bincode :: Decode :: decode (decoder) ?, ExchangeInstID : :: bincode :: Decode :: decode (decoder) ?, IPAddress : :: bincode :: Decode :: decode (decoder) ?,})}} impl < '__de , __Context > :: bincode :: BorrowDecode < '__de , __Context > for CThostFtdcExchangeForQuoteField {fn borrow_decode < __D : :: bincode :: de :: BorrowDecoder <'__de , Context = __Context > > (decoder : & mut __D) ->core :: result :: Result < Self , :: bincode :: error :: DecodeError > {core :: result :: Result :: Ok (Self {ForQuoteLocalID : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, ExchangeID : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, ParticipantID : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, ClientID : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, reserve1 : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, TraderID : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, InstallID : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, InsertDate : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, InsertTime : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, ForQuoteStatus : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, reserve2 : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, MacAddress : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, ExchangeInstID : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, IPAddress : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?,})}}