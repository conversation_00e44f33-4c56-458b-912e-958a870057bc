impl :: bincode :: Encode for CThostFtdcExchangeMarginRateField {fn encode < __E : :: bincode :: enc :: Encoder > (& self , encoder : & mut __E) ->core :: result :: Result < () , :: bincode :: error :: EncodeError > {:: bincode :: Encode :: encode (& self . BrokerID , encoder) ?; :: bincode :: Encode :: encode (& self . reserve1 , encoder) ?; :: bincode :: Encode :: encode (& self . <PERSON><PERSON><PERSON>lag , encoder) ?; :: bincode :: Encode :: encode (& self . LongMarginRatioByMoney , encoder) ?; :: bincode :: Encode :: encode (& self . LongMarginRatioByVolume , encoder) ?; :: bincode :: Encode :: encode (& self . ShortMarginRatioByMoney , encoder) ?; :: bincode :: Encode :: encode (& self . ShortMarginRatioByVolume , encoder) ?; :: bincode :: Encode :: encode (& self . ExchangeID , encoder) ?; :: bincode :: Encode :: encode (& self . InstrumentID , encoder) ?; core :: result :: Result :: Ok (())}}