impl < __Context > :: bincode :: Decode < __Context > for CThostFtdcExchangeOptionSelfCloseField {fn decode < __D : :: bincode :: de :: Decoder < Context = __Context > > (decoder : & mut __D) ->core :: result :: Result < Self , :: bincode :: error :: DecodeError > {core :: result :: Result :: Ok (Self {Volume : :: bincode :: Decode :: decode (decoder) ?, RequestID : :: bincode :: Decode :: decode (decoder) ?, BusinessUnit : :: bincode :: Decode :: decode (decoder) ?, HedgeFlag : :: bincode :: Decode :: decode (decoder) ?, OptSelfCloseFlag : :: bincode :: Decode :: decode (decoder) ?, OptionSelfCloseLocalID : :: bincode :: Decode :: decode (decoder) ?, ExchangeID : :: bincode :: Decode :: decode (decoder) ?, ParticipantID : :: bincode :: Decode :: decode (decoder) ?, ClientID : :: bincode :: Decode :: decode (decoder) ?, reserve1 : :: bincode :: Decode :: decode (decoder) ?, TraderID : :: bincode :: Decode :: decode (decoder) ?, InstallID : :: bincode :: Decode :: decode (decoder) ?, OrderSubmitStatus : :: bincode :: Decode :: decode (decoder) ?, NotifySequence : :: bincode :: Decode :: decode (decoder) ?, TradingDay : :: bincode :: Decode :: decode (decoder) ?, SettlementID : :: bincode :: Decode :: decode (decoder) ?, OptionSelfCloseSysID : :: bincode :: Decode :: decode (decoder) ?, InsertDate : :: bincode :: Decode :: decode (decoder) ?, InsertTime : :: bincode :: Decode :: decode (decoder) ?, CancelTime : :: bincode :: Decode :: decode (decoder) ?, ExecResult : :: bincode :: Decode :: decode (decoder) ?, ClearingPartID : :: bincode :: Decode :: decode (decoder) ?, SequenceNo : :: bincode :: Decode :: decode (decoder) ?, BranchID : :: bincode :: Decode :: decode (decoder) ?, reserve2 : :: bincode :: Decode :: decode (decoder) ?, MacAddress : :: bincode :: Decode :: decode (decoder) ?, ExchangeInstID : :: bincode :: Decode :: decode (decoder) ?, IPAddress : :: bincode :: Decode :: decode (decoder) ?,})}} impl < '__de , __Context > :: bincode :: BorrowDecode < '__de , __Context > for CThostFtdcExchangeOptionSelfCloseField {fn borrow_decode < __D : :: bincode :: de :: BorrowDecoder <'__de , Context = __Context > > (decoder : & mut __D) ->core :: result :: Result < Self , :: bincode :: error :: DecodeError > {core :: result :: Result :: Ok (Self {Volume : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, RequestID : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, BusinessUnit : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, HedgeFlag : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, OptSelfCloseFlag : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, OptionSelfCloseLocalID : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, ExchangeID : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, ParticipantID : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, ClientID : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, reserve1 : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, TraderID : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, InstallID : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, OrderSubmitStatus : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, NotifySequence : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, TradingDay : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, SettlementID : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, OptionSelfCloseSysID : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, InsertDate : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, InsertTime : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, CancelTime : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, ExecResult : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, ClearingPartID : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, SequenceNo : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, BranchID : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, reserve2 : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, MacAddress : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, ExchangeInstID : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, IPAddress : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?,})}}