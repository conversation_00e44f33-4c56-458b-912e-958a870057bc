impl :: bincode :: Encode for CThostFtdcExchangeQuoteField {fn encode < __E : :: bincode :: enc :: Encoder > (& self , encoder : & mut __E) ->core :: result :: Result < () , :: bincode :: error :: EncodeError > {:: bincode :: Encode :: encode (& self . AskPrice , encoder) ?; :: bincode :: Encode :: encode (& self . BidPrice , encoder) ?; :: bincode :: Encode :: encode (& self . AskVolume , encoder) ?; :: bincode :: Encode :: encode (& self . BidVolume , encoder) ?; :: bincode :: Encode :: encode (& self . RequestID , encoder) ?; :: bincode :: Encode :: encode (& self . BusinessUnit , encoder) ?; :: bincode :: Encode :: encode (& self . AskOffsetFlag , encoder) ?; :: bincode :: Encode :: encode (& self . BidOffsetFlag , encoder) ?; :: bincode :: Encode :: encode (& self . AskHedgeFlag , encoder) ?; :: bincode :: Encode :: encode (& self . BidHedgeFlag , encoder) ?; :: bincode :: Encode :: encode (& self . QuoteLocalID , encoder) ?; :: bincode :: Encode :: encode (& self . ExchangeID , encoder) ?; :: bincode :: Encode :: encode (& self . ParticipantID , encoder) ?; :: bincode :: Encode :: encode (& self . ClientID , encoder) ?; :: bincode :: Encode :: encode (& self . reserve1 , encoder) ?; :: bincode :: Encode :: encode (& self . TraderID , encoder) ?; :: bincode :: Encode :: encode (& self . InstallID , encoder) ?; :: bincode :: Encode :: encode (& self . NotifySequence , encoder) ?; :: bincode :: Encode :: encode (& self . OrderSubmitStatus , encoder) ?; :: bincode :: Encode :: encode (& self . TradingDay , encoder) ?; :: bincode :: Encode :: encode (& self . SettlementID , encoder) ?; :: bincode :: Encode :: encode (& self . QuoteSysID , encoder) ?; :: bincode :: Encode :: encode (& self . InsertDate , encoder) ?; :: bincode :: Encode :: encode (& self . InsertTime , encoder) ?; :: bincode :: Encode :: encode (& self . CancelTime , encoder) ?; :: bincode :: Encode :: encode (& self . QuoteStatus , encoder) ?; :: bincode :: Encode :: encode (& self . ClearingPartID , encoder) ?; :: bincode :: Encode :: encode (& self . SequenceNo , encoder) ?; :: bincode :: Encode :: encode (& self . AskOrderSysID , encoder) ?; :: bincode :: Encode :: encode (& self . BidOrderSysID , encoder) ?; :: bincode :: Encode :: encode (& self . ForQuoteSysID , encoder) ?; :: bincode :: Encode :: encode (& self . BranchID , encoder) ?; :: bincode :: Encode :: encode (& self . reserve2 , encoder) ?; :: bincode :: Encode :: encode (& self . MacAddress , encoder) ?; :: bincode :: Encode :: encode (& self . ExchangeInstID , encoder) ?; :: bincode :: Encode :: encode (& self . IPAddress , encoder) ?; core :: result :: Result :: Ok (())}}