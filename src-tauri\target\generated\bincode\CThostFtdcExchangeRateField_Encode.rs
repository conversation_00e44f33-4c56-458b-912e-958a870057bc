impl :: bincode :: Encode for CThostFtdcExchangeRateField {fn encode < __E : :: bincode :: enc :: Encoder > (& self , encoder : & mut __E) ->core :: result :: Result < () , :: bincode :: error :: EncodeError > {:: bincode :: Encode :: encode (& self . BrokerID , encoder) ?; :: bincode :: Encode :: encode (& self . FromCurrencyID , encoder) ?; :: bincode :: Encode :: encode (& self . FromCurrencyUnit , encoder) ?; :: bincode :: Encode :: encode (& self . ToCurrencyID , encoder) ?; :: bincode :: Encode :: encode (& self . ExchangeRate , encoder) ?; core :: result :: Result :: Ok (())}}