impl :: bincode :: Encode for CThostFtdcExchangeSequenceField {fn encode < __E : :: bincode :: enc :: Encoder > (& self , encoder : & mut __E) ->core :: result :: Result < () , :: bincode :: error :: EncodeError > {:: bincode :: Encode :: encode (& self . ExchangeID , encoder) ?; :: bincode :: Encode :: encode (& self . SequenceNo , encoder) ?; :: bincode :: Encode :: encode (& self . MarketStatus , encoder) ?; core :: result :: Result :: Ok (())}}