impl :: bincode :: Encode for CThostFtdcExchangeTradeField {fn encode < __E : :: bincode :: enc :: Encoder > (& self , encoder : & mut __E) ->core :: result :: Result < () , :: bincode :: error :: EncodeError > {:: bincode :: Encode :: encode (& self . ExchangeID , encoder) ?; :: bincode :: Encode :: encode (& self . TradeID , encoder) ?; :: bincode :: Encode :: encode (& self . Direction , encoder) ?; :: bincode :: Encode :: encode (& self . OrderSysID , encoder) ?; :: bincode :: Encode :: encode (& self . ParticipantID , encoder) ?; :: bincode :: Encode :: encode (& self . ClientID , encoder) ?; :: bincode :: Encode :: encode (& self . TradingRole , encoder) ?; :: bincode :: Encode :: encode (& self . reserve1 , encoder) ?; :: bincode :: Encode :: encode (& self . Offset<PERSON>lag , encoder) ?; :: bincode :: Encode :: encode (& self . HedgeFlag , encoder) ?; :: bincode :: Encode :: encode (& self . Price , encoder) ?; :: bincode :: Encode :: encode (& self . Volume , encoder) ?; :: bincode :: Encode :: encode (& self . TradeDate , encoder) ?; :: bincode :: Encode :: encode (& self . TradeTime , encoder) ?; :: bincode :: Encode :: encode (& self . TradeType , encoder) ?; :: bincode :: Encode :: encode (& self . PriceSource , encoder) ?; :: bincode :: Encode :: encode (& self . TraderID , encoder) ?; :: bincode :: Encode :: encode (& self . OrderLocalID , encoder) ?; :: bincode :: Encode :: encode (& self . ClearingPartID , encoder) ?; :: bincode :: Encode :: encode (& self . BusinessUnit , encoder) ?; :: bincode :: Encode :: encode (& self . SequenceNo , encoder) ?; :: bincode :: Encode :: encode (& self . TradeSource , encoder) ?; :: bincode :: Encode :: encode (& self . ExchangeInstID , encoder) ?; core :: result :: Result :: Ok (())}}