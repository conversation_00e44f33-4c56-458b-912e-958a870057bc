impl :: bincode :: Encode for CThostFtdcExecOrderActionField {fn encode < __E : :: bincode :: enc :: Encoder > (& self , encoder : & mut __E) ->core :: result :: Result < () , :: bincode :: error :: EncodeError > {:: bincode :: Encode :: encode (& self . BrokerID , encoder) ?; :: bincode :: Encode :: encode (& self . InvestorID , encoder) ?; :: bincode :: Encode :: encode (& self . ExecOrderActionRef , encoder) ?; :: bincode :: Encode :: encode (& self . ExecOrderRef , encoder) ?; :: bincode :: Encode :: encode (& self . RequestID , encoder) ?; :: bincode :: Encode :: encode (& self . FrontID , encoder) ?; :: bincode :: Encode :: encode (& self . SessionID , encoder) ?; :: bincode :: Encode :: encode (& self . ExchangeID , encoder) ?; :: bincode :: Encode :: encode (& self . ExecOrderSysID , encoder) ?; :: bincode :: Encode :: encode (& self . ActionFlag , encoder) ?; :: bincode :: Encode :: encode (& self . ActionDate , encoder) ?; :: bincode :: Encode :: encode (& self . ActionTime , encoder) ?; :: bincode :: Encode :: encode (& self . TraderID , encoder) ?; :: bincode :: Encode :: encode (& self . InstallID , encoder) ?; :: bincode :: Encode :: encode (& self . ExecOrderLocalID , encoder) ?; :: bincode :: Encode :: encode (& self . ActionLocalID , encoder) ?; :: bincode :: Encode :: encode (& self . ParticipantID , encoder) ?; :: bincode :: Encode :: encode (& self . ClientID , encoder) ?; :: bincode :: Encode :: encode (& self . BusinessUnit , encoder) ?; :: bincode :: Encode :: encode (& self . OrderActionStatus , encoder) ?; :: bincode :: Encode :: encode (& self . UserID , encoder) ?; :: bincode :: Encode :: encode (& self . ActionType , encoder) ?; :: bincode :: Encode :: encode (& self . StatusMsg , encoder) ?; :: bincode :: Encode :: encode (& self . reserve1 , encoder) ?; :: bincode :: Encode :: encode (& self . BranchID , encoder) ?; :: bincode :: Encode :: encode (& self . InvestUnitID , encoder) ?; :: bincode :: Encode :: encode (& self . reserve2 , encoder) ?; :: bincode :: Encode :: encode (& self . MacAddress , encoder) ?; :: bincode :: Encode :: encode (& self . InstrumentID , encoder) ?; :: bincode :: Encode :: encode (& self . IPAddress , encoder) ?; core :: result :: Result :: Ok (())}}