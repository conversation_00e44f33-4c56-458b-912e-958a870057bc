impl :: bincode :: Encode for CThostFtdcExecOrder<PERSON>ield {fn encode < __E : :: bincode :: enc :: Encoder > (& self , encoder : & mut __E) ->core :: result :: Result < () , :: bincode :: error :: EncodeError > {:: bincode :: Encode :: encode (& self . BrokerID , encoder) ?; :: bincode :: Encode :: encode (& self . InvestorID , encoder) ?; :: bincode :: Encode :: encode (& self . reserve1 , encoder) ?; :: bincode :: Encode :: encode (& self . ExecOrderRef , encoder) ?; :: bincode :: Encode :: encode (& self . UserID , encoder) ?; :: bincode :: Encode :: encode (& self . Volume , encoder) ?; :: bincode :: Encode :: encode (& self . RequestID , encoder) ?; :: bincode :: Encode :: encode (& self . BusinessUnit , encoder) ?; :: bincode :: Encode :: encode (& self . Offset<PERSON>lag , encoder) ?; :: bincode :: Encode :: encode (& self . HedgeFlag , encoder) ?; :: bincode :: Encode :: encode (& self . ActionType , encoder) ?; :: bincode :: Encode :: encode (& self . PosiDirection , encoder) ?; :: bincode :: Encode :: encode (& self . ReservePositionFlag , encoder) ?; :: bincode :: Encode :: encode (& self . CloseFlag , encoder) ?; :: bincode :: Encode :: encode (& self . ExecOrderLocalID , encoder) ?; :: bincode :: Encode :: encode (& self . ExchangeID , encoder) ?; :: bincode :: Encode :: encode (& self . ParticipantID , encoder) ?; :: bincode :: Encode :: encode (& self . ClientID , encoder) ?; :: bincode :: Encode :: encode (& self . reserve2 , encoder) ?; :: bincode :: Encode :: encode (& self . TraderID , encoder) ?; :: bincode :: Encode :: encode (& self . InstallID , encoder) ?; :: bincode :: Encode :: encode (& self . OrderSubmitStatus , encoder) ?; :: bincode :: Encode :: encode (& self . NotifySequence , encoder) ?; :: bincode :: Encode :: encode (& self . TradingDay , encoder) ?; :: bincode :: Encode :: encode (& self . SettlementID , encoder) ?; :: bincode :: Encode :: encode (& self . ExecOrderSysID , encoder) ?; :: bincode :: Encode :: encode (& self . InsertDate , encoder) ?; :: bincode :: Encode :: encode (& self . InsertTime , encoder) ?; :: bincode :: Encode :: encode (& self . CancelTime , encoder) ?; :: bincode :: Encode :: encode (& self . ExecResult , encoder) ?; :: bincode :: Encode :: encode (& self . ClearingPartID , encoder) ?; :: bincode :: Encode :: encode (& self . SequenceNo , encoder) ?; :: bincode :: Encode :: encode (& self . FrontID , encoder) ?; :: bincode :: Encode :: encode (& self . SessionID , encoder) ?; :: bincode :: Encode :: encode (& self . UserProductInfo , encoder) ?; :: bincode :: Encode :: encode (& self . StatusMsg , encoder) ?; :: bincode :: Encode :: encode (& self . ActiveUserID , encoder) ?; :: bincode :: Encode :: encode (& self . BrokerExecOrderSeq , encoder) ?; :: bincode :: Encode :: encode (& self . BranchID , encoder) ?; :: bincode :: Encode :: encode (& self . InvestUnitID , encoder) ?; :: bincode :: Encode :: encode (& self . AccountID , encoder) ?; :: bincode :: Encode :: encode (& self . CurrencyID , encoder) ?; :: bincode :: Encode :: encode (& self . reserve3 , encoder) ?; :: bincode :: Encode :: encode (& self . MacAddress , encoder) ?; :: bincode :: Encode :: encode (& self . InstrumentID , encoder) ?; :: bincode :: Encode :: encode (& self . ExchangeInstID , encoder) ?; :: bincode :: Encode :: encode (& self . IPAddress , encoder) ?; core :: result :: Result :: Ok (())}}