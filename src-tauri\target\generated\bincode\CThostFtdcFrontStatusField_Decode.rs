impl < __Context > :: bincode :: Decode < __Context > for CThostFtdcFrontStatusField {fn decode < __D : :: bincode :: de :: Decoder < Context = __Context > > (decoder : & mut __D) ->core :: result :: Result < Self , :: bincode :: error :: DecodeError > {core :: result :: Result :: Ok (Self {FrontID : :: bincode :: Decode :: decode (decoder) ?, LastReportDate : :: bincode :: Decode :: decode (decoder) ?, LastReportTime : :: bincode :: Decode :: decode (decoder) ?, IsActive : :: bincode :: Decode :: decode (decoder) ?,})}} impl < '__de , __Context > :: bincode :: BorrowDecode < '__de , __Context > for CThostFtdcFrontStatusField {fn borrow_decode < __D : :: bincode :: de :: BorrowDecoder <'__de , Context = __Context > > (decoder : & mut __D) ->core :: result :: Result < Self , :: bincode :: error :: DecodeError > {core :: result :: Result :: Ok (Self {FrontID : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, LastReportDate : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, LastReportTime : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, IsActive : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?,})}}