impl :: bincode :: Encode for CThostFtdcFrontStatusField {fn encode < __E : :: bincode :: enc :: Encoder > (& self , encoder : & mut __E) ->core :: result :: Result < () , :: bincode :: error :: EncodeError > {:: bincode :: Encode :: encode (& self . FrontID , encoder) ?; :: bincode :: Encode :: encode (& self . LastReportDate , encoder) ?; :: bincode :: Encode :: encode (& self . LastReportTime , encoder) ?; :: bincode :: Encode :: encode (& self . IsActive , encoder) ?; core :: result :: Result :: Ok (())}}