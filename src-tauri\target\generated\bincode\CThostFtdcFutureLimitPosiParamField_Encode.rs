impl :: bincode :: Encode for CThostFtdcFutureLimitPosiParamField {fn encode < __E : :: bincode :: enc :: Encoder > (& self , encoder : & mut __E) ->core :: result :: Result < () , :: bincode :: error :: EncodeError > {:: bincode :: Encode :: encode (& self . InvestorRange , encoder) ?; :: bincode :: Encode :: encode (& self . BrokerID , encoder) ?; :: bincode :: Encode :: encode (& self . InvestorID , encoder) ?; :: bincode :: Encode :: encode (& self . reserve1 , encoder) ?; :: bincode :: Encode :: encode (& self . SpecOpenVolume , encoder) ?; :: bincode :: Encode :: encode (& self . ArbiOpenVolume , encoder) ?; :: bincode :: Encode :: encode (& self . OpenVolume , encoder) ?; :: bincode :: Encode :: encode (& self . ProductID , encoder) ?; core :: result :: Result :: Ok (())}}