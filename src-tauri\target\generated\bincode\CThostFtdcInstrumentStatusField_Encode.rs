impl :: bincode :: Encode for CThostFtdcInstrumentStatusField {fn encode < __E : :: bincode :: enc :: Encoder > (& self , encoder : & mut __E) ->core :: result :: Result < () , :: bincode :: error :: EncodeError > {:: bincode :: Encode :: encode (& self . ExchangeID , encoder) ?; :: bincode :: Encode :: encode (& self . reserve1 , encoder) ?; :: bincode :: Encode :: encode (& self . SettlementGroupID , encoder) ?; :: bincode :: Encode :: encode (& self . reserve2 , encoder) ?; :: bincode :: Encode :: encode (& self . InstrumentStatus , encoder) ?; :: bincode :: Encode :: encode (& self . TradingSegmentSN , encoder) ?; :: bincode :: Encode :: encode (& self . EnterTime , encoder) ?; :: bincode :: Encode :: encode (& self . EnterReason , encoder) ?; :: bincode :: Encode :: encode (& self . ExchangeInstID , encoder) ?; :: bincode :: Encode :: encode (& self . InstrumentID , encoder) ?; core :: result :: Result :: Ok (())}}