impl :: bincode :: Encode for CThostFtdcInvestorGroupField {fn encode < __E : :: bincode :: enc :: Encoder > (& self , encoder : & mut __E) ->core :: result :: Result < () , :: bincode :: error :: EncodeError > {:: bincode :: Encode :: encode (& self . BrokerID , encoder) ?; :: bincode :: Encode :: encode (& self . InvestorGroupID , encoder) ?; :: bincode :: Encode :: encode (& self . InvestorGroupName , encoder) ?; core :: result :: Result :: Ok (())}}