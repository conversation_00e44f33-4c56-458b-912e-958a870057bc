impl < __Context > :: bincode :: Decode < __Context > for CThostFtdcInvestorPositionCombineDetailField {fn decode < __D : :: bincode :: de :: Decoder < Context = __Context > > (decoder : & mut __D) ->core :: result :: Result < Self , :: bincode :: error :: DecodeError > {core :: result :: Result :: Ok (Self {TradingDay : :: bincode :: Decode :: decode (decoder) ?, OpenDate : :: bincode :: Decode :: decode (decoder) ?, ExchangeID : :: bincode :: Decode :: decode (decoder) ?, SettlementID : :: bincode :: Decode :: decode (decoder) ?, BrokerID : :: bincode :: Decode :: decode (decoder) ?, InvestorID : :: bincode :: Decode :: decode (decoder) ?, ComTradeID : :: bincode :: Decode :: decode (decoder) ?, TradeID : :: bincode :: Decode :: decode (decoder) ?, reserve1 : :: bincode :: Decode :: decode (decoder) ?, HedgeFlag : :: bincode :: Decode :: decode (decoder) ?, Direction : :: bincode :: Decode :: decode (decoder) ?, TotalAmt : :: bincode :: Decode :: decode (decoder) ?, Margin : :: bincode :: Decode :: decode (decoder) ?, ExchMargin : :: bincode :: Decode :: decode (decoder) ?, MarginRateByMoney : :: bincode :: Decode :: decode (decoder) ?, MarginRateByVolume : :: bincode :: Decode :: decode (decoder) ?, LegID : :: bincode :: Decode :: decode (decoder) ?, LegMultiple : :: bincode :: Decode :: decode (decoder) ?, reserve2 : :: bincode :: Decode :: decode (decoder) ?, TradeGroupID : :: bincode :: Decode :: decode (decoder) ?, InvestUnitID : :: bincode :: Decode :: decode (decoder) ?, InstrumentID : :: bincode :: Decode :: decode (decoder) ?, CombInstrumentID : :: bincode :: Decode :: decode (decoder) ?,})}} impl < '__de , __Context > :: bincode :: BorrowDecode < '__de , __Context > for CThostFtdcInvestorPositionCombineDetailField {fn borrow_decode < __D : :: bincode :: de :: BorrowDecoder <'__de , Context = __Context > > (decoder : & mut __D) ->core :: result :: Result < Self , :: bincode :: error :: DecodeError > {core :: result :: Result :: Ok (Self {TradingDay : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, OpenDate : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, ExchangeID : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, SettlementID : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, BrokerID : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, InvestorID : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, ComTradeID : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, TradeID : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, reserve1 : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, HedgeFlag : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, Direction : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, TotalAmt : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, Margin : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, ExchMargin : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, MarginRateByMoney : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, MarginRateByVolume : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, LegID : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, LegMultiple : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, reserve2 : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, TradeGroupID : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, InvestUnitID : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, InstrumentID : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, CombInstrumentID : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?,})}}