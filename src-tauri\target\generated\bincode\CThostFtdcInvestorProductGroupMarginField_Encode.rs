impl :: bincode :: Encode for CThostFtdcInvestorProductGroupMarginField {fn encode < __E : :: bincode :: enc :: Encoder > (& self , encoder : & mut __E) ->core :: result :: Result < () , :: bincode :: error :: EncodeError > {:: bincode :: Encode :: encode (& self . reserve1 , encoder) ?; :: bincode :: Encode :: encode (& self . BrokerID , encoder) ?; :: bincode :: Encode :: encode (& self . InvestorID , encoder) ?; :: bincode :: Encode :: encode (& self . TradingDay , encoder) ?; :: bincode :: Encode :: encode (& self . SettlementID , encoder) ?; :: bincode :: Encode :: encode (& self . FrozenMargin , encoder) ?; :: bincode :: Encode :: encode (& self . LongFrozenMargin , encoder) ?; :: bincode :: Encode :: encode (& self . ShortFrozenMargin , encoder) ?; :: bincode :: Encode :: encode (& self . UseMargin , encoder) ?; :: bincode :: Encode :: encode (& self . LongUseMargin , encoder) ?; :: bincode :: Encode :: encode (& self . ShortUseMargin , encoder) ?; :: bincode :: Encode :: encode (& self . ExchMargin , encoder) ?; :: bincode :: Encode :: encode (& self . LongExchMargin , encoder) ?; :: bincode :: Encode :: encode (& self . ShortExchMargin , encoder) ?; :: bincode :: Encode :: encode (& self . CloseProfit , encoder) ?; :: bincode :: Encode :: encode (& self . FrozenCommission , encoder) ?; :: bincode :: Encode :: encode (& self . Commission , encoder) ?; :: bincode :: Encode :: encode (& self . FrozenCash , encoder) ?; :: bincode :: Encode :: encode (& self . CashIn , encoder) ?; :: bincode :: Encode :: encode (& self . PositionProfit , encoder) ?; :: bincode :: Encode :: encode (& self . OffsetAmount , encoder) ?; :: bincode :: Encode :: encode (& self . LongOffsetAmount , encoder) ?; :: bincode :: Encode :: encode (& self . ShortOffsetAmount , encoder) ?; :: bincode :: Encode :: encode (& self . ExchOffsetAmount , encoder) ?; :: bincode :: Encode :: encode (& self . LongExchOffsetAmount , encoder) ?; :: bincode :: Encode :: encode (& self . ShortExchOffsetAmount , encoder) ?; :: bincode :: Encode :: encode (& self . HedgeFlag , encoder) ?; :: bincode :: Encode :: encode (& self . ExchangeID , encoder) ?; :: bincode :: Encode :: encode (& self . InvestUnitID , encoder) ?; :: bincode :: Encode :: encode (& self . ProductGroupID , encoder) ?; core :: result :: Result :: Ok (())}}