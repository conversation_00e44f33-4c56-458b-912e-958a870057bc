impl < __Context > :: bincode :: Decode < __Context > for CThostFtdcLinkManField {fn decode < __D : :: bincode :: de :: Decoder < Context = __Context > > (decoder : & mut __D) ->core :: result :: Result < Self , :: bincode :: error :: DecodeError > {core :: result :: Result :: Ok (Self {BrokerID : :: bincode :: Decode :: decode (decoder) ?, InvestorID : :: bincode :: Decode :: decode (decoder) ?, PersonType : :: bincode :: Decode :: decode (decoder) ?, IdentifiedCardType : :: bincode :: Decode :: decode (decoder) ?, IdentifiedCardNo : :: bincode :: Decode :: decode (decoder) ?, PersonName : :: bincode :: Decode :: decode (decoder) ?, Telephone : :: bincode :: Decode :: decode (decoder) ?, Address : :: bincode :: Decode :: decode (decoder) ?, ZipCode : :: bincode :: Decode :: decode (decoder) ?, Priority : :: bincode :: Decode :: decode (decoder) ?, UOAZipCode : :: bincode :: Decode :: decode (decoder) ?, PersonFullName : :: bincode :: Decode :: decode (decoder) ?,})}} impl < '__de , __Context > :: bincode :: BorrowDecode < '__de , __Context > for CThostFtdcLinkManField {fn borrow_decode < __D : :: bincode :: de :: BorrowDecoder <'__de , Context = __Context > > (decoder : & mut __D) ->core :: result :: Result < Self , :: bincode :: error :: DecodeError > {core :: result :: Result :: Ok (Self {BrokerID : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, InvestorID : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, PersonType : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, IdentifiedCardType : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, IdentifiedCardNo : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, PersonName : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, Telephone : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, Address : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, ZipCode : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, Priority : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, UOAZipCode : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, PersonFullName : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?,})}}