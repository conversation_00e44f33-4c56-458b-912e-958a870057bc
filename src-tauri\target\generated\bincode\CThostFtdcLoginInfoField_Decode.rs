impl < __Context > :: bincode :: Decode < __Context > for CThostFtdcLoginInfoField {fn decode < __D : :: bincode :: de :: Decoder < Context = __Context > > (decoder : & mut __D) ->core :: result :: Result < Self , :: bincode :: error :: DecodeError > {core :: result :: Result :: Ok (Self {FrontID : :: bincode :: Decode :: decode (decoder) ?, SessionID : :: bincode :: Decode :: decode (decoder) ?, BrokerID : :: bincode :: Decode :: decode (decoder) ?, UserID : :: bincode :: Decode :: decode (decoder) ?, LoginDate : :: bincode :: Decode :: decode (decoder) ?, LoginTime : :: bincode :: Decode :: decode (decoder) ?, reserve1 : :: bincode :: Decode :: decode (decoder) ?, UserProductInfo : :: bincode :: Decode :: decode (decoder) ?, InterfaceProductInfo : :: bincode :: Decode :: decode (decoder) ?, ProtocolInfo : :: bincode :: Decode :: decode (decoder) ?, SystemName : :: bincode :: Decode :: decode (decoder) ?, PasswordDeprecated : :: bincode :: Decode :: decode (decoder) ?, MaxOrderRef : :: bincode :: Decode :: decode (decoder) ?, SHFETime : :: bincode :: Decode :: decode (decoder) ?, DCETime : :: bincode :: Decode :: decode (decoder) ?, CZCETime : :: bincode :: Decode :: decode (decoder) ?, FFEXTime : :: bincode :: Decode :: decode (decoder) ?, MacAddress : :: bincode :: Decode :: decode (decoder) ?, OneTimePassword : :: bincode :: Decode :: decode (decoder) ?, INETime : :: bincode :: Decode :: decode (decoder) ?, IsQryControl : :: bincode :: Decode :: decode (decoder) ?, LoginRemark : :: bincode :: Decode :: decode (decoder) ?, Password : :: bincode :: Decode :: decode (decoder) ?, IPAddress : :: bincode :: Decode :: decode (decoder) ?,})}} impl < '__de , __Context > :: bincode :: BorrowDecode < '__de , __Context > for CThostFtdcLoginInfoField {fn borrow_decode < __D : :: bincode :: de :: BorrowDecoder <'__de , Context = __Context > > (decoder : & mut __D) ->core :: result :: Result < Self , :: bincode :: error :: DecodeError > {core :: result :: Result :: Ok (Self {FrontID : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, SessionID : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, BrokerID : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, UserID : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, LoginDate : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, LoginTime : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, reserve1 : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, UserProductInfo : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, InterfaceProductInfo : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, ProtocolInfo : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, SystemName : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, PasswordDeprecated : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, MaxOrderRef : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, SHFETime : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, DCETime : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, CZCETime : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, FFEXTime : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, MacAddress : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, OneTimePassword : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, INETime : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, IsQryControl : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, LoginRemark : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, Password : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, IPAddress : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?,})}}