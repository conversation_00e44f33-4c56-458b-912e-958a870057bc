impl :: bincode :: Encode for CThostFtdcManualSyncBrokerUserOTPField {fn encode < __E : :: bincode :: enc :: Encoder > (& self , encoder : & mut __E) ->core :: result :: Result < () , :: bincode :: error :: EncodeError > {:: bincode :: Encode :: encode (& self . BrokerID , encoder) ?; :: bincode :: Encode :: encode (& self . UserID , encoder) ?; :: bincode :: Encode :: encode (& self . OTPType , encoder) ?; :: bincode :: Encode :: encode (& self . FirstOTP , encoder) ?; :: bincode :: Encode :: encode (& self . SecondOTP , encoder) ?; core :: result :: Result :: Ok (())}}