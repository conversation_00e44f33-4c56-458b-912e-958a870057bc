impl :: bincode :: Encode for CThostFtdcMarketDataAsk23Field {fn encode < __E : :: bincode :: enc :: Encoder > (& self , encoder : & mut __E) ->core :: result :: Result < () , :: bincode :: error :: EncodeError > {:: bincode :: Encode :: encode (& self . AskPrice2 , encoder) ?; :: bincode :: Encode :: encode (& self . AskVolume2 , encoder) ?; :: bincode :: Encode :: encode (& self . AskPrice3 , encoder) ?; :: bincode :: Encode :: encode (& self . AskVolume3 , encoder) ?; core :: result :: Result :: Ok (())}}