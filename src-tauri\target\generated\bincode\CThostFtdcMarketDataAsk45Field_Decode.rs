impl < __Context > :: bincode :: Decode < __Context > for CThostFtdcMarketDataAsk45Field {fn decode < __D : :: bincode :: de :: Decoder < Context = __Context > > (decoder : & mut __D) ->core :: result :: Result < Self , :: bincode :: error :: DecodeError > {core :: result :: Result :: Ok (Self {AskPrice4 : :: bincode :: Decode :: decode (decoder) ?, AskVolume4 : :: bincode :: Decode :: decode (decoder) ?, AskPrice5 : :: bincode :: Decode :: decode (decoder) ?, AskVolume5 : :: bincode :: Decode :: decode (decoder) ?,})}} impl < '__de , __Context > :: bincode :: BorrowDecode < '__de , __Context > for CThostFtdcMarketDataAsk45Field {fn borrow_decode < __D : :: bincode :: de :: BorrowDecoder <'__de , Context = __Context > > (decoder : & mut __D) ->core :: result :: Result < Self , :: bincode :: error :: DecodeError > {core :: result :: Result :: Ok (Self {AskPrice4 : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, AskVolume4 : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, AskPrice5 : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, AskVolume5 : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?,})}}