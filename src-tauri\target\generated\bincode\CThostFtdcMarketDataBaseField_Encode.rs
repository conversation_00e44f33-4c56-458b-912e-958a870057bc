impl :: bincode :: Encode for CThostFtdcMarketDataBaseField {fn encode < __E : :: bincode :: enc :: Encoder > (& self , encoder : & mut __E) ->core :: result :: Result < () , :: bincode :: error :: EncodeError > {:: bincode :: Encode :: encode (& self . TradingDay , encoder) ?; :: bincode :: Encode :: encode (& self . PreSettlementPrice , encoder) ?; :: bincode :: Encode :: encode (& self . PreClosePrice , encoder) ?; :: bincode :: Encode :: encode (& self . PreOpenInterest , encoder) ?; :: bincode :: Encode :: encode (& self . PreDelta , encoder) ?; core :: result :: Result :: Ok (())}}