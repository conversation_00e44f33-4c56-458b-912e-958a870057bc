impl < __Context > :: bincode :: Decode < __Context > for CThostFtdcMarketDataBestPriceField {fn decode < __D : :: bincode :: de :: Decoder < Context = __Context > > (decoder : & mut __D) ->core :: result :: Result < Self , :: bincode :: error :: DecodeError > {core :: result :: Result :: Ok (Self {BidPrice1 : :: bincode :: Decode :: decode (decoder) ?, BidVolume1 : :: bincode :: Decode :: decode (decoder) ?, AskPrice1 : :: bincode :: Decode :: decode (decoder) ?, AskVolume1 : :: bincode :: Decode :: decode (decoder) ?,})}} impl < '__de , __Context > :: bincode :: BorrowDecode < '__de , __Context > for CThostFtdcMarketDataBestPriceField {fn borrow_decode < __D : :: bincode :: de :: BorrowDecoder <'__de , Context = __Context > > (decoder : & mut __D) ->core :: result :: Result < Self , :: bincode :: error :: DecodeError > {core :: result :: Result :: Ok (Self {BidPrice1 : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, BidVolume1 : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, AskPrice1 : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, AskVolume1 : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?,})}}