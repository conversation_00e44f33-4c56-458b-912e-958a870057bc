impl :: bincode :: Encode for CThostFtdcMarketDataBestPriceField {fn encode < __E : :: bincode :: enc :: Encoder > (& self , encoder : & mut __E) ->core :: result :: Result < () , :: bincode :: error :: EncodeError > {:: bincode :: Encode :: encode (& self . BidPrice1 , encoder) ?; :: bincode :: Encode :: encode (& self . BidVolume1 , encoder) ?; :: bincode :: Encode :: encode (& self . AskPrice1 , encoder) ?; :: bincode :: Encode :: encode (& self . AskVolume1 , encoder) ?; core :: result :: Result :: Ok (())}}