impl :: bincode :: Encode for CThostFtdcMarketDataBid23Field {fn encode < __E : :: bincode :: enc :: Encoder > (& self , encoder : & mut __E) ->core :: result :: Result < () , :: bincode :: error :: EncodeError > {:: bincode :: Encode :: encode (& self . BidPrice2 , encoder) ?; :: bincode :: Encode :: encode (& self . BidVolume2 , encoder) ?; :: bincode :: Encode :: encode (& self . BidPrice3 , encoder) ?; :: bincode :: Encode :: encode (& self . BidVolume3 , encoder) ?; core :: result :: Result :: Ok (())}}