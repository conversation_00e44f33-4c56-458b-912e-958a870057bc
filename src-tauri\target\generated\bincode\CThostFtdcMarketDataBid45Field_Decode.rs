impl < __Context > :: bincode :: Decode < __Context > for CThostFtdcMarketDataBid45Field {fn decode < __D : :: bincode :: de :: Decoder < Context = __Context > > (decoder : & mut __D) ->core :: result :: Result < Self , :: bincode :: error :: DecodeError > {core :: result :: Result :: Ok (Self {BidPrice4 : :: bincode :: Decode :: decode (decoder) ?, BidVolume4 : :: bincode :: Decode :: decode (decoder) ?, BidPrice5 : :: bincode :: Decode :: decode (decoder) ?, BidVolume5 : :: bincode :: Decode :: decode (decoder) ?,})}} impl < '__de , __Context > :: bincode :: BorrowDecode < '__de , __Context > for CThostFtdcMarketDataBid45Field {fn borrow_decode < __D : :: bincode :: de :: BorrowDecoder <'__de , Context = __Context > > (decoder : & mut __D) ->core :: result :: Result < Self , :: bincode :: error :: DecodeError > {core :: result :: Result :: Ok (Self {BidPrice4 : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, BidVolume4 : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, BidPrice5 : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, BidVolume5 : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?,})}}