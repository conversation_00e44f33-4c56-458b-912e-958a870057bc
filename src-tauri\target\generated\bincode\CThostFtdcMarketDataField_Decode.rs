impl < __Context > :: bincode :: Decode < __Context > for CThostFtdcMarketDataField {fn decode < __D : :: bincode :: de :: Decoder < Context = __Context > > (decoder : & mut __D) ->core :: result :: Result < Self , :: bincode :: error :: DecodeError > {core :: result :: Result :: Ok (Self {TradingDay : :: bincode :: Decode :: decode (decoder) ?, reserve1 : :: bincode :: Decode :: decode (decoder) ?, ExchangeID : :: bincode :: Decode :: decode (decoder) ?, reserve2 : :: bincode :: Decode :: decode (decoder) ?, LastPrice : :: bincode :: Decode :: decode (decoder) ?, PreSettlementPrice : :: bincode :: Decode :: decode (decoder) ?, PreClosePrice : :: bincode :: Decode :: decode (decoder) ?, PreOpenInterest : :: bincode :: Decode :: decode (decoder) ?, OpenPrice : :: bincode :: Decode :: decode (decoder) ?, HighestPrice : :: bincode :: Decode :: decode (decoder) ?, LowestPrice : :: bincode :: Decode :: decode (decoder) ?, Volume : :: bincode :: Decode :: decode (decoder) ?, Turnover : :: bincode :: Decode :: decode (decoder) ?, OpenInterest : :: bincode :: Decode :: decode (decoder) ?, ClosePrice : :: bincode :: Decode :: decode (decoder) ?, SettlementPrice : :: bincode :: Decode :: decode (decoder) ?, UpperLimitPrice : :: bincode :: Decode :: decode (decoder) ?, LowerLimitPrice : :: bincode :: Decode :: decode (decoder) ?, PreDelta : :: bincode :: Decode :: decode (decoder) ?, CurrDelta : :: bincode :: Decode :: decode (decoder) ?, UpdateTime : :: bincode :: Decode :: decode (decoder) ?, UpdateMillisec : :: bincode :: Decode :: decode (decoder) ?, ActionDay : :: bincode :: Decode :: decode (decoder) ?, InstrumentID : :: bincode :: Decode :: decode (decoder) ?, ExchangeInstID : :: bincode :: Decode :: decode (decoder) ?,})}} impl < '__de , __Context > :: bincode :: BorrowDecode < '__de , __Context > for CThostFtdcMarketDataField {fn borrow_decode < __D : :: bincode :: de :: BorrowDecoder <'__de , Context = __Context > > (decoder : & mut __D) ->core :: result :: Result < Self , :: bincode :: error :: DecodeError > {core :: result :: Result :: Ok (Self {TradingDay : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, reserve1 : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, ExchangeID : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, reserve2 : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, LastPrice : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, PreSettlementPrice : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, PreClosePrice : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, PreOpenInterest : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, OpenPrice : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, HighestPrice : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, LowestPrice : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, Volume : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, Turnover : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, OpenInterest : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, ClosePrice : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, SettlementPrice : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, UpperLimitPrice : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, LowerLimitPrice : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, PreDelta : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, CurrDelta : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, UpdateTime : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, UpdateMillisec : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, ActionDay : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, InstrumentID : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, ExchangeInstID : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?,})}}