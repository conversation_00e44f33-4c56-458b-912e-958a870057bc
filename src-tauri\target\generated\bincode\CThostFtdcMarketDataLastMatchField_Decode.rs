impl < __Context > :: bincode :: Decode < __Context > for CThostFtdcMarketDataLastMatchField {fn decode < __D : :: bincode :: de :: Decoder < Context = __Context > > (decoder : & mut __D) ->core :: result :: Result < Self , :: bincode :: error :: DecodeError > {core :: result :: Result :: Ok (Self {LastPrice : :: bincode :: Decode :: decode (decoder) ?, Volume : :: bincode :: Decode :: decode (decoder) ?, Turnover : :: bincode :: Decode :: decode (decoder) ?, OpenInterest : :: bincode :: Decode :: decode (decoder) ?,})}} impl < '__de , __Context > :: bincode :: BorrowDecode < '__de , __Context > for CThostFtdcMarketDataLastMatchField {fn borrow_decode < __D : :: bincode :: de :: BorrowDecoder <'__de , Context = __Context > > (decoder : & mut __D) ->core :: result :: Result < Self , :: bincode :: error :: DecodeError > {core :: result :: Result :: Ok (Self {LastPrice : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, Volume : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, Turnover : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, OpenInterest : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?,})}}