impl :: bincode :: Encode for CThostFtdcMarketDataLastMatchField {fn encode < __E : :: bincode :: enc :: Encoder > (& self , encoder : & mut __E) ->core :: result :: Result < () , :: bincode :: error :: EncodeError > {:: bincode :: Encode :: encode (& self . LastPrice , encoder) ?; :: bincode :: Encode :: encode (& self . Volume , encoder) ?; :: bincode :: Encode :: encode (& self . Turnover , encoder) ?; :: bincode :: Encode :: encode (& self . OpenInterest , encoder) ?; core :: result :: Result :: Ok (())}}