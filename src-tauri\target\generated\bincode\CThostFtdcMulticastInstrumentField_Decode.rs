impl < __Context > :: bincode :: Decode < __Context > for CThostFtdcMulticastInstrumentField {fn decode < __D : :: bincode :: de :: Decoder < Context = __Context > > (decoder : & mut __D) ->core :: result :: Result < Self , :: bincode :: error :: DecodeError > {core :: result :: Result :: Ok (Self {TopicID : :: bincode :: Decode :: decode (decoder) ?, reserve1 : :: bincode :: Decode :: decode (decoder) ?, InstrumentNo : :: bincode :: Decode :: decode (decoder) ?, CodePrice : :: bincode :: Decode :: decode (decoder) ?, VolumeMultiple : :: bincode :: Decode :: decode (decoder) ?, PriceTick : :: bincode :: Decode :: decode (decoder) ?, InstrumentID : :: bincode :: Decode :: decode (decoder) ?,})}} impl < '__de , __Context > :: bincode :: BorrowDecode < '__de , __Context > for CThostFtdcMulticastInstrumentField {fn borrow_decode < __D : :: bincode :: de :: BorrowDecoder <'__de , Context = __Context > > (decoder : & mut __D) ->core :: result :: Result < Self , :: bincode :: error :: DecodeError > {core :: result :: Result :: Ok (Self {TopicID : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, reserve1 : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, InstrumentNo : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, CodePrice : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, VolumeMultiple : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, PriceTick : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, InstrumentID : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?,})}}