impl :: bincode :: Encode for CThostFtdcNoticeField {fn encode < __E : :: bincode :: enc :: Encoder > (& self , encoder : & mut __E) ->core :: result :: Result < () , :: bincode :: error :: EncodeError > {:: bincode :: Encode :: encode (& self . BrokerID , encoder) ?; :: bincode :: Encode :: encode (& self . Content , encoder) ?; :: bincode :: Encode :: encode (& self . SequenceLabel , encoder) ?; core :: result :: Result :: Ok (())}}