impl :: bincode :: Encode for CThostFtdcNotifyFutureSignOutField {fn encode < __E : :: bincode :: enc :: Encoder > (& self , encoder : & mut __E) ->core :: result :: Result < () , :: bincode :: error :: EncodeError > {:: bincode :: Encode :: encode (& self . TradeCode , encoder) ?; :: bincode :: Encode :: encode (& self . BankID , encoder) ?; :: bincode :: Encode :: encode (& self . BankBranchID , encoder) ?; :: bincode :: Encode :: encode (& self . BrokerID , encoder) ?; :: bincode :: Encode :: encode (& self . BrokerBranchID , encoder) ?; :: bincode :: Encode :: encode (& self . TradeDate , encoder) ?; :: bincode :: Encode :: encode (& self . TradeTime , encoder) ?; :: bincode :: Encode :: encode (& self . BankSerial , encoder) ?; :: bincode :: Encode :: encode (& self . TradingDay , encoder) ?; :: bincode :: Encode :: encode (& self . PlateSerial , encoder) ?; :: bincode :: Encode :: encode (& self . LastFragment , encoder) ?; :: bincode :: Encode :: encode (& self . SessionID , encoder) ?; :: bincode :: Encode :: encode (& self . InstallID , encoder) ?; :: bincode :: Encode :: encode (& self . UserID , encoder) ?; :: bincode :: Encode :: encode (& self . Digest , encoder) ?; :: bincode :: Encode :: encode (& self . CurrencyID , encoder) ?; :: bincode :: Encode :: encode (& self . DeviceID , encoder) ?; :: bincode :: Encode :: encode (& self . BrokerIDByBank , encoder) ?; :: bincode :: Encode :: encode (& self . OperNo , encoder) ?; :: bincode :: Encode :: encode (& self . RequestID , encoder) ?; :: bincode :: Encode :: encode (& self . TID , encoder) ?; :: bincode :: Encode :: encode (& self . ErrorID , encoder) ?; :: bincode :: Encode :: encode (& self . ErrorMsg , encoder) ?; core :: result :: Result :: Ok (())}}