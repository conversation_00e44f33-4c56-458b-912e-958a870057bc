impl :: bincode :: Encode for CThostFtdcOptionInstrTradeCostField {fn encode < __E : :: bincode :: enc :: Encoder > (& self , encoder : & mut __E) ->core :: result :: Result < () , :: bincode :: error :: EncodeError > {:: bincode :: Encode :: encode (& self . BrokerID , encoder) ?; :: bincode :: Encode :: encode (& self . InvestorID , encoder) ?; :: bincode :: Encode :: encode (& self . reserve1 , encoder) ?; :: bincode :: Encode :: encode (& self . HedgeFlag , encoder) ?; :: bincode :: Encode :: encode (& self . FixedMargin , encoder) ?; :: bincode :: Encode :: encode (& self . MiniMargin , encoder) ?; :: bincode :: Encode :: encode (& self . Royalty , encoder) ?; :: bincode :: Encode :: encode (& self . ExchFixedMargin , encoder) ?; :: bincode :: Encode :: encode (& self . ExchMiniMargin , encoder) ?; :: bincode :: Encode :: encode (& self . ExchangeID , encoder) ?; :: bincode :: Encode :: encode (& self . InvestUnitID , encoder) ?; :: bincode :: Encode :: encode (& self . InstrumentID , encoder) ?; core :: result :: Result :: Ok (())}}