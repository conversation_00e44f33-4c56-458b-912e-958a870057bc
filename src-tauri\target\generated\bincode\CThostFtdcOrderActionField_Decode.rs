impl < __Context > :: bincode :: Decode < __Context > for CThostFtdcOrderActionField {fn decode < __D : :: bincode :: de :: Decoder < Context = __Context > > (decoder : & mut __D) ->core :: result :: Result < Self , :: bincode :: error :: DecodeError > {core :: result :: Result :: Ok (Self {BrokerID : :: bincode :: Decode :: decode (decoder) ?, InvestorID : :: bincode :: Decode :: decode (decoder) ?, OrderActionRef : :: bincode :: Decode :: decode (decoder) ?, OrderRef : :: bincode :: Decode :: decode (decoder) ?, RequestID : :: bincode :: Decode :: decode (decoder) ?, FrontID : :: bincode :: Decode :: decode (decoder) ?, SessionID : :: bincode :: Decode :: decode (decoder) ?, ExchangeID : :: bincode :: Decode :: decode (decoder) ?, OrderSysID : :: bincode :: Decode :: decode (decoder) ?, ActionFlag : :: bincode :: Decode :: decode (decoder) ?, LimitPrice : :: bincode :: Decode :: decode (decoder) ?, VolumeChange : :: bincode :: Decode :: decode (decoder) ?, ActionDate : :: bincode :: Decode :: decode (decoder) ?, ActionTime : :: bincode :: Decode :: decode (decoder) ?, TraderID : :: bincode :: Decode :: decode (decoder) ?, InstallID : :: bincode :: Decode :: decode (decoder) ?, OrderLocalID : :: bincode :: Decode :: decode (decoder) ?, ActionLocalID : :: bincode :: Decode :: decode (decoder) ?, ParticipantID : :: bincode :: Decode :: decode (decoder) ?, ClientID : :: bincode :: Decode :: decode (decoder) ?, BusinessUnit : :: bincode :: Decode :: decode (decoder) ?, OrderActionStatus : :: bincode :: Decode :: decode (decoder) ?, UserID : :: bincode :: Decode :: decode (decoder) ?, StatusMsg : :: bincode :: Decode :: decode (decoder) ?, reserve1 : :: bincode :: Decode :: decode (decoder) ?, BranchID : :: bincode :: Decode :: decode (decoder) ?, InvestUnitID : :: bincode :: Decode :: decode (decoder) ?, reserve2 : :: bincode :: Decode :: decode (decoder) ?, MacAddress : :: bincode :: Decode :: decode (decoder) ?, InstrumentID : :: bincode :: Decode :: decode (decoder) ?, IPAddress : :: bincode :: Decode :: decode (decoder) ?,})}} impl < '__de , __Context > :: bincode :: BorrowDecode < '__de , __Context > for CThostFtdcOrderActionField {fn borrow_decode < __D : :: bincode :: de :: BorrowDecoder <'__de , Context = __Context > > (decoder : & mut __D) ->core :: result :: Result < Self , :: bincode :: error :: DecodeError > {core :: result :: Result :: Ok (Self {BrokerID : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, InvestorID : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, OrderActionRef : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, OrderRef : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, RequestID : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, FrontID : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, SessionID : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, ExchangeID : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, OrderSysID : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, ActionFlag : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, LimitPrice : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, VolumeChange : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, ActionDate : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, ActionTime : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, TraderID : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, InstallID : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, OrderLocalID : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, ActionLocalID : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, ParticipantID : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, ClientID : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, BusinessUnit : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, OrderActionStatus : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, UserID : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, StatusMsg : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, reserve1 : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, BranchID : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, InvestUnitID : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, reserve2 : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, MacAddress : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, InstrumentID : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, IPAddress : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?,})}}