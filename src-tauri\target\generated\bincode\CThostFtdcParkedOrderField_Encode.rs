impl :: bincode :: Encode for CThostFtdcParkedOrderField {fn encode < __E : :: bincode :: enc :: Encoder > (& self , encoder : & mut __E) ->core :: result :: Result < () , :: bincode :: error :: EncodeError > {:: bincode :: Encode :: encode (& self . BrokerID , encoder) ?; :: bincode :: Encode :: encode (& self . InvestorID , encoder) ?; :: bincode :: Encode :: encode (& self . reserve1 , encoder) ?; :: bincode :: Encode :: encode (& self . OrderRef , encoder) ?; :: bincode :: Encode :: encode (& self . UserID , encoder) ?; :: bincode :: Encode :: encode (& self . OrderPriceType , encoder) ?; :: bincode :: Encode :: encode (& self . Direction , encoder) ?; :: bincode :: Encode :: encode (& self . CombOffsetFlag , encoder) ?; :: bincode :: Encode :: encode (& self . CombHedgeFlag , encoder) ?; :: bincode :: Encode :: encode (& self . LimitPrice , encoder) ?; :: bincode :: Encode :: encode (& self . VolumeTotalOriginal , encoder) ?; :: bincode :: Encode :: encode (& self . TimeCondition , encoder) ?; :: bincode :: Encode :: encode (& self . GTDDate , encoder) ?; :: bincode :: Encode :: encode (& self . VolumeCondition , encoder) ?; :: bincode :: Encode :: encode (& self . MinVolume , encoder) ?; :: bincode :: Encode :: encode (& self . ContingentCondition , encoder) ?; :: bincode :: Encode :: encode (& self . StopPrice , encoder) ?; :: bincode :: Encode :: encode (& self . ForceCloseReason , encoder) ?; :: bincode :: Encode :: encode (& self . IsAutoSuspend , encoder) ?; :: bincode :: Encode :: encode (& self . BusinessUnit , encoder) ?; :: bincode :: Encode :: encode (& self . RequestID , encoder) ?; :: bincode :: Encode :: encode (& self . UserForceClose , encoder) ?; :: bincode :: Encode :: encode (& self . ExchangeID , encoder) ?; :: bincode :: Encode :: encode (& self . ParkedOrderID , encoder) ?; :: bincode :: Encode :: encode (& self . UserType , encoder) ?; :: bincode :: Encode :: encode (& self . Status , encoder) ?; :: bincode :: Encode :: encode (& self . ErrorID , encoder) ?; :: bincode :: Encode :: encode (& self . ErrorMsg , encoder) ?; :: bincode :: Encode :: encode (& self . IsSwapOrder , encoder) ?; :: bincode :: Encode :: encode (& self . AccountID , encoder) ?; :: bincode :: Encode :: encode (& self . CurrencyID , encoder) ?; :: bincode :: Encode :: encode (& self . ClientID , encoder) ?; :: bincode :: Encode :: encode (& self . InvestUnitID , encoder) ?; :: bincode :: Encode :: encode (& self . reserve2 , encoder) ?; :: bincode :: Encode :: encode (& self . MacAddress , encoder) ?; :: bincode :: Encode :: encode (& self . InstrumentID , encoder) ?; :: bincode :: Encode :: encode (& self . IPAddress , encoder) ?; core :: result :: Result :: Ok (())}}