impl < __Context > :: bincode :: Decode < __Context > for CThostFtdcProductField {fn decode < __D : :: bincode :: de :: Decoder < Context = __Context > > (decoder : & mut __D) ->core :: result :: Result < Self , :: bincode :: error :: DecodeError > {core :: result :: Result :: Ok (Self {reserve1 : :: bincode :: Decode :: decode (decoder) ?, ProductName : :: bincode :: Decode :: decode (decoder) ?, ExchangeID : :: bincode :: Decode :: decode (decoder) ?, ProductClass : :: bincode :: Decode :: decode (decoder) ?, VolumeMultiple : :: bincode :: Decode :: decode (decoder) ?, PriceTick : :: bincode :: Decode :: decode (decoder) ?, MaxMarketOrderVolume : :: bincode :: Decode :: decode (decoder) ?, MinMarketOrderVolume : :: bincode :: Decode :: decode (decoder) ?, MaxLimitOrderVolume : :: bincode :: Decode :: decode (decoder) ?, MinLimitOrderVolume : :: bincode :: Decode :: decode (decoder) ?, PositionType : :: bincode :: Decode :: decode (decoder) ?, PositionDateType : :: bincode :: Decode :: decode (decoder) ?, CloseDealType : :: bincode :: Decode :: decode (decoder) ?, TradeCurrencyID : :: bincode :: Decode :: decode (decoder) ?, MortgageFundUseRange : :: bincode :: Decode :: decode (decoder) ?, reserve2 : :: bincode :: Decode :: decode (decoder) ?, UnderlyingMultiple : :: bincode :: Decode :: decode (decoder) ?, ProductID : :: bincode :: Decode :: decode (decoder) ?, ExchangeProductID : :: bincode :: Decode :: decode (decoder) ?,})}} impl < '__de , __Context > :: bincode :: BorrowDecode < '__de , __Context > for CThostFtdcProductField {fn borrow_decode < __D : :: bincode :: de :: BorrowDecoder <'__de , Context = __Context > > (decoder : & mut __D) ->core :: result :: Result < Self , :: bincode :: error :: DecodeError > {core :: result :: Result :: Ok (Self {reserve1 : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, ProductName : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, ExchangeID : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, ProductClass : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, VolumeMultiple : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, PriceTick : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, MaxMarketOrderVolume : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, MinMarketOrderVolume : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, MaxLimitOrderVolume : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, MinLimitOrderVolume : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, PositionType : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, PositionDateType : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, CloseDealType : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, TradeCurrencyID : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, MortgageFundUseRange : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, reserve2 : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, UnderlyingMultiple : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, ProductID : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, ExchangeProductID : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?,})}}