impl :: bincode :: Encode for CThostFtdcProductGroupField {fn encode < __E : :: bincode :: enc :: Encoder > (& self , encoder : & mut __E) ->core :: result :: Result < () , :: bincode :: error :: EncodeError > {:: bincode :: Encode :: encode (& self . reserve1 , encoder) ?; :: bincode :: Encode :: encode (& self . ExchangeID , encoder) ?; :: bincode :: Encode :: encode (& self . reserve2 , encoder) ?; :: bincode :: Encode :: encode (& self . ProductID , encoder) ?; :: bincode :: Encode :: encode (& self . ProductGroupID , encoder) ?; core :: result :: Result :: Ok (())}}