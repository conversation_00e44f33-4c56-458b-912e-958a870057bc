impl :: bincode :: Encode for CThostFtdcQryBulletinField {fn encode < __E : :: bincode :: enc :: Encoder > (& self , encoder : & mut __E) ->core :: result :: Result < () , :: bincode :: error :: EncodeError > {:: bincode :: Encode :: encode (& self . ExchangeID , encoder) ?; :: bincode :: Encode :: encode (& self . BulletinID , encoder) ?; :: bincode :: Encode :: encode (& self . SequenceNo , encoder) ?; :: bincode :: Encode :: encode (& self . NewsType , encoder) ?; :: bincode :: Encode :: encode (& self . NewsUrgency , encoder) ?; core :: result :: Result :: Ok (())}}