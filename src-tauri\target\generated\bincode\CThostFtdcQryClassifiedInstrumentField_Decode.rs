impl < __Context > :: bincode :: Decode < __Context > for CThostFtdcQryClassifiedInstrumentField {fn decode < __D : :: bincode :: de :: Decoder < Context = __Context > > (decoder : & mut __D) ->core :: result :: Result < Self , :: bincode :: error :: DecodeError > {core :: result :: Result :: Ok (Self {InstrumentID : :: bincode :: Decode :: decode (decoder) ?, ExchangeID : :: bincode :: Decode :: decode (decoder) ?, ExchangeInstID : :: bincode :: Decode :: decode (decoder) ?, ProductID : :: bincode :: Decode :: decode (decoder) ?, TradingType : :: bincode :: Decode :: decode (decoder) ?, ClassType : :: bincode :: Decode :: decode (decoder) ?,})}} impl < '__de , __Context > :: bincode :: BorrowDecode < '__de , __Context > for CThostFtdcQryClassifiedInstrumentField {fn borrow_decode < __D : :: bincode :: de :: BorrowDecoder <'__de , Context = __Context > > (decoder : & mut __D) ->core :: result :: Result < Self , :: bincode :: error :: DecodeError > {core :: result :: Result :: Ok (Self {InstrumentID : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, ExchangeID : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, ExchangeInstID : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, ProductID : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, TradingType : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, ClassType : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?,})}}