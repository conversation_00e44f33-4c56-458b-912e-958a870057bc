impl :: bincode :: Encode for CThostFtdcQryExchangeExecOrderField {fn encode < __E : :: bincode :: enc :: Encoder > (& self , encoder : & mut __E) ->core :: result :: Result < () , :: bincode :: error :: EncodeError > {:: bincode :: Encode :: encode (& self . ParticipantID , encoder) ?; :: bincode :: Encode :: encode (& self . ClientID , encoder) ?; :: bincode :: Encode :: encode (& self . reserve1 , encoder) ?; :: bincode :: Encode :: encode (& self . ExchangeID , encoder) ?; :: bincode :: Encode :: encode (& self . TraderID , encoder) ?; :: bincode :: Encode :: encode (& self . ExchangeInstID , encoder) ?; core :: result :: Result :: Ok (())}}