impl :: bincode :: Encode for CThostFtdcQrySecAgentACIDMapField {fn encode < __E : :: bincode :: enc :: Encoder > (& self , encoder : & mut __E) ->core :: result :: Result < () , :: bincode :: error :: EncodeError > {:: bincode :: Encode :: encode (& self . BrokerID , encoder) ?; :: bincode :: Encode :: encode (& self . UserID , encoder) ?; :: bincode :: Encode :: encode (& self . AccountID , encoder) ?; :: bincode :: Encode :: encode (& self . CurrencyID , encoder) ?; core :: result :: Result :: Ok (())}}