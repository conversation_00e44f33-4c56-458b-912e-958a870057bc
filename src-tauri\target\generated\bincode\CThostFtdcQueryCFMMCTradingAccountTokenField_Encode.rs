impl :: bincode :: Encode for CThostFtdcQueryCFMMCTradingAccountTokenField {fn encode < __E : :: bincode :: enc :: Encoder > (& self , encoder : & mut __E) ->core :: result :: Result < () , :: bincode :: error :: EncodeError > {:: bincode :: Encode :: encode (& self . BrokerID , encoder) ?; :: bincode :: Encode :: encode (& self . InvestorID , encoder) ?; :: bincode :: Encode :: encode (& self . InvestUnitID , encoder) ?; core :: result :: Result :: Ok (())}}