impl :: bincode :: Encode for CThostFtdcReqChangeAccountField {fn encode < __E : :: bincode :: enc :: Encoder > (& self , encoder : & mut __E) ->core :: result :: Result < () , :: bincode :: error :: EncodeError > {:: bincode :: Encode :: encode (& self . TradeCode , encoder) ?; :: bincode :: Encode :: encode (& self . BankID , encoder) ?; :: bincode :: Encode :: encode (& self . BankBranchID , encoder) ?; :: bincode :: Encode :: encode (& self . BrokerID , encoder) ?; :: bincode :: Encode :: encode (& self . BrokerBranchID , encoder) ?; :: bincode :: Encode :: encode (& self . TradeDate , encoder) ?; :: bincode :: Encode :: encode (& self . TradeTime , encoder) ?; :: bincode :: Encode :: encode (& self . BankSerial , encoder) ?; :: bincode :: Encode :: encode (& self . TradingDay , encoder) ?; :: bincode :: Encode :: encode (& self . PlateSerial , encoder) ?; :: bincode :: Encode :: encode (& self . LastFragment , encoder) ?; :: bincode :: Encode :: encode (& self . SessionID , encoder) ?; :: bincode :: Encode :: encode (& self . CustomerName , encoder) ?; :: bincode :: Encode :: encode (& self . IdCardType , encoder) ?; :: bincode :: Encode :: encode (& self . IdentifiedCardNo , encoder) ?; :: bincode :: Encode :: encode (& self . Gender , encoder) ?; :: bincode :: Encode :: encode (& self . CountryCode , encoder) ?; :: bincode :: Encode :: encode (& self . CustType , encoder) ?; :: bincode :: Encode :: encode (& self . Address , encoder) ?; :: bincode :: Encode :: encode (& self . ZipCode , encoder) ?; :: bincode :: Encode :: encode (& self . Telephone , encoder) ?; :: bincode :: Encode :: encode (& self . MobilePhone , encoder) ?; :: bincode :: Encode :: encode (& self . Fax , encoder) ?; :: bincode :: Encode :: encode (& self . EMail , encoder) ?; :: bincode :: Encode :: encode (& self . MoneyAccountStatus , encoder) ?; :: bincode :: Encode :: encode (& self . BankAccount , encoder) ?; :: bincode :: Encode :: encode (& self . BankPassWord , encoder) ?; :: bincode :: Encode :: encode (& self . NewBankAccount , encoder) ?; :: bincode :: Encode :: encode (& self . NewBankPassWord , encoder) ?; :: bincode :: Encode :: encode (& self . AccountID , encoder) ?; :: bincode :: Encode :: encode (& self . Password , encoder) ?; :: bincode :: Encode :: encode (& self . BankAccType , encoder) ?; :: bincode :: Encode :: encode (& self . InstallID , encoder) ?; :: bincode :: Encode :: encode (& self . VerifyCertNoFlag , encoder) ?; :: bincode :: Encode :: encode (& self . CurrencyID , encoder) ?; :: bincode :: Encode :: encode (& self . BrokerIDByBank , encoder) ?; :: bincode :: Encode :: encode (& self . BankPwdFlag , encoder) ?; :: bincode :: Encode :: encode (& self . SecuPwdFlag , encoder) ?; :: bincode :: Encode :: encode (& self . TID , encoder) ?; :: bincode :: Encode :: encode (& self . Digest , encoder) ?; :: bincode :: Encode :: encode (& self . LongCustomerName , encoder) ?; core :: result :: Result :: Ok (())}}