impl < __Context > :: bincode :: Decode < __Context > for CThostFtdcReqDayEndFileReadyField {fn decode < __D : :: bincode :: de :: Decoder < Context = __Context > > (decoder : & mut __D) ->core :: result :: Result < Self , :: bincode :: error :: DecodeError > {core :: result :: Result :: Ok (Self {TradeCode : :: bincode :: Decode :: decode (decoder) ?, BankID : :: bincode :: Decode :: decode (decoder) ?, BankBranchID : :: bincode :: Decode :: decode (decoder) ?, BrokerID : :: bincode :: Decode :: decode (decoder) ?, BrokerBranchID : :: bincode :: Decode :: decode (decoder) ?, TradeDate : :: bincode :: Decode :: decode (decoder) ?, TradeTime : :: bincode :: Decode :: decode (decoder) ?, BankSerial : :: bincode :: Decode :: decode (decoder) ?, TradingDay : :: bincode :: Decode :: decode (decoder) ?, PlateSerial : :: bincode :: Decode :: decode (decoder) ?, LastFragment : :: bincode :: Decode :: decode (decoder) ?, SessionID : :: bincode :: Decode :: decode (decoder) ?, FileBusinessCode : :: bincode :: Decode :: decode (decoder) ?, Digest : :: bincode :: Decode :: decode (decoder) ?,})}} impl < '__de , __Context > :: bincode :: BorrowDecode < '__de , __Context > for CThostFtdcReqDayEndFileReadyField {fn borrow_decode < __D : :: bincode :: de :: BorrowDecoder <'__de , Context = __Context > > (decoder : & mut __D) ->core :: result :: Result < Self , :: bincode :: error :: DecodeError > {core :: result :: Result :: Ok (Self {TradeCode : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, BankID : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, BankBranchID : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, BrokerID : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, BrokerBranchID : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, TradeDate : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, TradeTime : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, BankSerial : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, TradingDay : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, PlateSerial : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, LastFragment : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, SessionID : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, FileBusinessCode : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, Digest : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?,})}}