impl < __Context > :: bincode :: Decode < __Context > for CThostFtdcReqQueryTradeResultBySerialField {fn decode < __D : :: bincode :: de :: Decoder < Context = __Context > > (decoder : & mut __D) ->core :: result :: Result < Self , :: bincode :: error :: DecodeError > {core :: result :: Result :: Ok (Self {TradeCode : :: bincode :: Decode :: decode (decoder) ?, BankID : :: bincode :: Decode :: decode (decoder) ?, BankBranchID : :: bincode :: Decode :: decode (decoder) ?, BrokerID : :: bincode :: Decode :: decode (decoder) ?, BrokerBranchID : :: bincode :: Decode :: decode (decoder) ?, TradeDate : :: bincode :: Decode :: decode (decoder) ?, TradeTime : :: bincode :: Decode :: decode (decoder) ?, BankSerial : :: bincode :: Decode :: decode (decoder) ?, TradingDay : :: bincode :: Decode :: decode (decoder) ?, PlateSerial : :: bincode :: Decode :: decode (decoder) ?, LastFragment : :: bincode :: Decode :: decode (decoder) ?, SessionID : :: bincode :: Decode :: decode (decoder) ?, Reference : :: bincode :: Decode :: decode (decoder) ?, RefrenceIssureType : :: bincode :: Decode :: decode (decoder) ?, RefrenceIssure : :: bincode :: Decode :: decode (decoder) ?, CustomerName : :: bincode :: Decode :: decode (decoder) ?, IdCardType : :: bincode :: Decode :: decode (decoder) ?, IdentifiedCardNo : :: bincode :: Decode :: decode (decoder) ?, CustType : :: bincode :: Decode :: decode (decoder) ?, BankAccount : :: bincode :: Decode :: decode (decoder) ?, BankPassWord : :: bincode :: Decode :: decode (decoder) ?, AccountID : :: bincode :: Decode :: decode (decoder) ?, Password : :: bincode :: Decode :: decode (decoder) ?, CurrencyID : :: bincode :: Decode :: decode (decoder) ?, TradeAmount : :: bincode :: Decode :: decode (decoder) ?, Digest : :: bincode :: Decode :: decode (decoder) ?, LongCustomerName : :: bincode :: Decode :: decode (decoder) ?,})}} impl < '__de , __Context > :: bincode :: BorrowDecode < '__de , __Context > for CThostFtdcReqQueryTradeResultBySerialField {fn borrow_decode < __D : :: bincode :: de :: BorrowDecoder <'__de , Context = __Context > > (decoder : & mut __D) ->core :: result :: Result < Self , :: bincode :: error :: DecodeError > {core :: result :: Result :: Ok (Self {TradeCode : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, BankID : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, BankBranchID : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, BrokerID : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, BrokerBranchID : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, TradeDate : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, TradeTime : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, BankSerial : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, TradingDay : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, PlateSerial : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, LastFragment : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, SessionID : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, Reference : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, RefrenceIssureType : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, RefrenceIssure : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, CustomerName : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, IdCardType : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, IdentifiedCardNo : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, CustType : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, BankAccount : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, BankPassWord : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, AccountID : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, Password : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, CurrencyID : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, TradeAmount : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, Digest : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, LongCustomerName : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?,})}}