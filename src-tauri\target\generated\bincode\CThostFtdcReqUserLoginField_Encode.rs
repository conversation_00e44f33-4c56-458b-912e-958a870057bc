impl :: bincode :: Encode for CThostFtdcReqUserLoginField {fn encode < __E : :: bincode :: enc :: Encoder > (& self , encoder : & mut __E) ->core :: result :: Result < () , :: bincode :: error :: EncodeError > {:: bincode :: Encode :: encode (& self . TradingDay , encoder) ?; :: bincode :: Encode :: encode (& self . BrokerID , encoder) ?; :: bincode :: Encode :: encode (& self . UserID , encoder) ?; :: bincode :: Encode :: encode (& self . Password , encoder) ?; :: bincode :: Encode :: encode (& self . UserProductInfo , encoder) ?; :: bincode :: Encode :: encode (& self . InterfaceProductInfo , encoder) ?; :: bincode :: Encode :: encode (& self . ProtocolInfo , encoder) ?; :: bincode :: Encode :: encode (& self . <PERSON><PERSON><PERSON><PERSON> , encoder) ?; :: bincode :: Encode :: encode (& self . OneTimePassword , encoder) ?; :: bincode :: Encode :: encode (& self . reserve1 , encoder) ?; :: bincode :: Encode :: encode (& self . LoginRemark , encoder) ?; :: bincode :: Encode :: encode (& self . ClientIPPort , encoder) ?; :: bincode :: Encode :: encode (& self . ClientIPAddress , encoder) ?; core :: result :: Result :: Ok (())}}