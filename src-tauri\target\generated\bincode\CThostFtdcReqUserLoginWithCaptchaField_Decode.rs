impl < __Context > :: bincode :: Decode < __Context > for CThostFtdcReqUserLoginWithCaptchaField {fn decode < __D : :: bincode :: de :: Decoder < Context = __Context > > (decoder : & mut __D) ->core :: result :: Result < Self , :: bincode :: error :: DecodeError > {core :: result :: Result :: Ok (Self {TradingDay : :: bincode :: Decode :: decode (decoder) ?, BrokerID : :: bincode :: Decode :: decode (decoder) ?, UserID : :: bincode :: Decode :: decode (decoder) ?, Password : :: bincode :: Decode :: decode (decoder) ?, UserProductInfo : :: bincode :: Decode :: decode (decoder) ?, InterfaceProductInfo : :: bincode :: Decode :: decode (decoder) ?, ProtocolInfo : :: bincode :: Decode :: decode (decoder) ?, MacAddress : :: bincode :: Decode :: decode (decoder) ?, reserve1 : :: bincode :: Decode :: decode (decoder) ?, LoginRemark : :: bincode :: Decode :: decode (decoder) ?, Captcha : :: bincode :: Decode :: decode (decoder) ?, ClientIPPort : :: bincode :: Decode :: decode (decoder) ?, ClientIPAddress : :: bincode :: Decode :: decode (decoder) ?,})}} impl < '__de , __Context > :: bincode :: BorrowDecode < '__de , __Context > for CThostFtdcReqUserLoginWithCaptchaField {fn borrow_decode < __D : :: bincode :: de :: BorrowDecoder <'__de , Context = __Context > > (decoder : & mut __D) ->core :: result :: Result < Self , :: bincode :: error :: DecodeError > {core :: result :: Result :: Ok (Self {TradingDay : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, BrokerID : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, UserID : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, Password : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, UserProductInfo : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, InterfaceProductInfo : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, ProtocolInfo : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, MacAddress : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, reserve1 : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, LoginRemark : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, Captcha : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, ClientIPPort : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, ClientIPAddress : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?,})}}