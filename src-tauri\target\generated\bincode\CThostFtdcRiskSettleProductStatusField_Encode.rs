impl :: bincode :: Encode for CThostFtdcRiskSettleProductStatusField {fn encode < __E : :: bincode :: enc :: Encoder > (& self , encoder : & mut __E) ->core :: result :: Result < () , :: bincode :: error :: EncodeError > {:: bincode :: Encode :: encode (& self . ExchangeID , encoder) ?; :: bincode :: Encode :: encode (& self . ProductID , encoder) ?; :: bincode :: Encode :: encode (& self . ProductStatus , encoder) ?; core :: result :: Result :: Ok (())}}