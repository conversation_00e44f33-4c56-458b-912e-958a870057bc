impl :: bincode :: Encode for CThostFtdcSyncDelaySwapFrozenField {fn encode < __E : :: bincode :: enc :: Encoder > (& self , encoder : & mut __E) ->core :: result :: Result < () , :: bincode :: error :: EncodeError > {:: bincode :: Encode :: encode (& self . DelaySwapSeqNo , encoder) ?; :: bincode :: Encode :: encode (& self . BrokerID , encoder) ?; :: bincode :: Encode :: encode (& self . InvestorID , encoder) ?; :: bincode :: Encode :: encode (& self . FromCurrencyID , encoder) ?; :: bincode :: Encode :: encode (& self . FromRemainSwap , encoder) ?; :: bincode :: Encode :: encode (& self . IsManualSwap , encoder) ?; core :: result :: Result :: Ok (())}}