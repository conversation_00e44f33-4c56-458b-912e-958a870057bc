impl :: bincode :: Encode for CThostFtdcSyncDeltaDceCombInstrumentField {fn encode < __E : :: bincode :: enc :: Encoder > (& self , encoder : & mut __E) ->core :: result :: Result < () , :: bincode :: error :: EncodeError > {:: bincode :: Encode :: encode (& self . CombInstrumentID , encoder) ?; :: bincode :: Encode :: encode (& self . ExchangeID , encoder) ?; :: bincode :: Encode :: encode (& self . ExchangeInstID , encoder) ?; :: bincode :: Encode :: encode (& self . TradeGroupID , encoder) ?; :: bincode :: Encode :: encode (& self . CombHedgeFlag , encoder) ?; :: bincode :: Encode :: encode (& self . CombinationType , encoder) ?; :: bincode :: Encode :: encode (& self . Direction , encoder) ?; :: bincode :: Encode :: encode (& self . ProductID , encoder) ?; :: bincode :: Encode :: encode (& self . Xparameter , encoder) ?; :: bincode :: Encode :: encode (& self . ActionDirection , encoder) ?; :: bincode :: Encode :: encode (& self . SyncDeltaSequenceNo , encoder) ?; core :: result :: Result :: Ok (())}}