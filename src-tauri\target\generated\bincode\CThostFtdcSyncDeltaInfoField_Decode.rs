impl < __Context > :: bincode :: Decode < __Context > for CThostFtdcSyncDeltaInfoField {fn decode < __D : :: bincode :: de :: Decoder < Context = __Context > > (decoder : & mut __D) ->core :: result :: Result < Self , :: bincode :: error :: DecodeError > {core :: result :: Result :: Ok (Self {SyncDeltaSequenceNo : :: bincode :: Decode :: decode (decoder) ?, SyncDeltaStatus : :: bincode :: Decode :: decode (decoder) ?, SyncDescription : :: bincode :: Decode :: decode (decoder) ?, IsOnlyTrdDelta : :: bincode :: Decode :: decode (decoder) ?,})}} impl < '__de , __Context > :: bincode :: BorrowDecode < '__de , __Context > for CThostFtdcSyncDeltaInfoField {fn borrow_decode < __D : :: bincode :: de :: BorrowDecoder <'__de , Context = __Context > > (decoder : & mut __D) ->core :: result :: Result < Self , :: bincode :: error :: DecodeError > {core :: result :: Result :: Ok (Self {SyncDeltaSequenceNo : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, SyncDeltaStatus : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, SyncDescription : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, IsOnlyTrdDelta : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?,})}}