impl :: bincode :: Encode for CThostFtdcSyncDeltaInfoField {fn encode < __E : :: bincode :: enc :: Encoder > (& self , encoder : & mut __E) ->core :: result :: Result < () , :: bincode :: error :: EncodeError > {:: bincode :: Encode :: encode (& self . SyncDeltaSequenceNo , encoder) ?; :: bincode :: Encode :: encode (& self . SyncDeltaStatus , encoder) ?; :: bincode :: Encode :: encode (& self . SyncDescription , encoder) ?; :: bincode :: Encode :: encode (& self . IsOnlyTrdDelta , encoder) ?; core :: result :: Result :: Ok (())}}