impl :: bincode :: Encode for CThostFtdcSyncDeltaInitInvstMarginField {fn encode < __E : :: bincode :: enc :: Encoder > (& self , encoder : & mut __E) ->core :: result :: Result < () , :: bincode :: error :: EncodeError > {:: bincode :: Encode :: encode (& self . Broker<PERSON> , encoder) ?; :: bincode :: Encode :: encode (& self . InvestorID , encoder) ?; :: bincode :: Encode :: encode (& self . LastRiskTotalInvstMargin , encoder) ?; :: bincode :: Encode :: encode (& self . LastRiskTotalExchMargin , encoder) ?; :: bincode :: Encode :: encode (& self . ThisSyncInvstMargin , encoder) ?; :: bincode :: Encode :: encode (& self . ThisSyncExchMargin , encoder) ?; :: bincode :: Encode :: encode (& self . RemainRiskInvstMargin , encoder) ?; :: bincode :: Encode :: encode (& self . RemainRiskExchMargin , encoder) ?; :: bincode :: Encode :: encode (& self . LastRiskSpecTotalInvstMargin , encoder) ?; :: bincode :: Encode :: encode (& self . LastRiskSpecTotalExchMargin , encoder) ?; :: bincode :: Encode :: encode (& self . ThisSyncSpecInvstMargin , encoder) ?; :: bincode :: Encode :: encode (& self . ThisSyncSpecExchMargin , encoder) ?; :: bincode :: Encode :: encode (& self . RemainRiskSpecInvstMargin , encoder) ?; :: bincode :: Encode :: encode (& self . RemainRiskSpecExchMargin , encoder) ?; :: bincode :: Encode :: encode (& self . SyncDeltaSequenceNo , encoder) ?; core :: result :: Result :: Ok (())}}