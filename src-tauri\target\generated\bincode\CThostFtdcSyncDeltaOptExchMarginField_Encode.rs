impl :: bincode :: Encode for CThostFtdcSyncDeltaOptExchMarginField {fn encode < __E : :: bincode :: enc :: Encoder > (& self , encoder : & mut __E) ->core :: result :: Result < () , :: bincode :: error :: EncodeError > {:: bincode :: Encode :: encode (& self . BrokerID , encoder) ?; :: bincode :: Encode :: encode (& self . InstrumentID , encoder) ?; :: bincode :: Encode :: encode (& self . SShortMarginRatioByMoney , encoder) ?; :: bincode :: Encode :: encode (& self . SShortMarginRatioByVolume , encoder) ?; :: bincode :: Encode :: encode (& self . HShortMarginRatioByMoney , encoder) ?; :: bincode :: Encode :: encode (& self . HShortMarginRatioByVolume , encoder) ?; :: bincode :: Encode :: encode (& self . AShortMarginRatioByMoney , encoder) ?; :: bincode :: Encode :: encode (& self . AShortMarginRatioByVolume , encoder) ?; :: bincode :: Encode :: encode (& self . MShortMarginRatioByMoney , encoder) ?; :: bincode :: Encode :: encode (& self . MShortMarginRatioByVolume , encoder) ?; :: bincode :: Encode :: encode (& self . ActionDirection , encoder) ?; :: bincode :: Encode :: encode (& self . SyncDeltaSequenceNo , encoder) ?; core :: result :: Result :: Ok (())}}