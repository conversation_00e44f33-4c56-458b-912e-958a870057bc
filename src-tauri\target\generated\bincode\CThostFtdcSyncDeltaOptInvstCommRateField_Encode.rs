impl :: bincode :: Encode for CThostFtdcSyncDeltaOptInvstCommRateField {fn encode < __E : :: bincode :: enc :: Encoder > (& self , encoder : & mut __E) ->core :: result :: Result < () , :: bincode :: error :: EncodeError > {:: bincode :: Encode :: encode (& self . InstrumentID , encoder) ?; :: bincode :: Encode :: encode (& self . InvestorRange , encoder) ?; :: bincode :: Encode :: encode (& self . BrokerID , encoder) ?; :: bincode :: Encode :: encode (& self . InvestorID , encoder) ?; :: bincode :: Encode :: encode (& self . OpenRatioByMoney , encoder) ?; :: bincode :: Encode :: encode (& self . OpenRatioByVolume , encoder) ?; :: bincode :: Encode :: encode (& self . CloseRatioByMoney , encoder) ?; :: bincode :: Encode :: encode (& self . CloseRatioByVolume , encoder) ?; :: bincode :: Encode :: encode (& self . CloseTodayRatioByMoney , encoder) ?; :: bincode :: Encode :: encode (& self . CloseTodayRatioByVolume , encoder) ?; :: bincode :: Encode :: encode (& self . StrikeRatioByMoney , encoder) ?; :: bincode :: Encode :: encode (& self . StrikeRatioByVolume , encoder) ?; :: bincode :: Encode :: encode (& self . ActionDirection , encoder) ?; :: bincode :: Encode :: encode (& self . SyncDeltaSequenceNo , encoder) ?; core :: result :: Result :: Ok (())}}