impl < __Context > :: bincode :: Decode < __Context > for CThostFtdcSyncDeltaOptInvstMarginField {fn decode < __D : :: bincode :: de :: Decoder < Context = __Context > > (decoder : & mut __D) ->core :: result :: Result < Self , :: bincode :: error :: DecodeError > {core :: result :: Result :: Ok (Self {InstrumentID : :: bincode :: Decode :: decode (decoder) ?, InvestorRange : :: bincode :: Decode :: decode (decoder) ?, BrokerID : :: bincode :: Decode :: decode (decoder) ?, InvestorID : :: bincode :: Decode :: decode (decoder) ?, SShortMarginRatioByMoney : :: bincode :: Decode :: decode (decoder) ?, SShortMarginRatioByVolume : :: bincode :: Decode :: decode (decoder) ?, HShortMarginRatioByMoney : :: bincode :: Decode :: decode (decoder) ?, HShortMarginRatioByVolume : :: bincode :: Decode :: decode (decoder) ?, AShortMarginRatioByMoney : :: bincode :: Decode :: decode (decoder) ?, AShortMarginRatioByVolume : :: bincode :: Decode :: decode (decoder) ?, IsRelative : :: bincode :: Decode :: decode (decoder) ?, MShortMarginRatioByMoney : :: bincode :: Decode :: decode (decoder) ?, MShortMarginRatioByVolume : :: bincode :: Decode :: decode (decoder) ?, ActionDirection : :: bincode :: Decode :: decode (decoder) ?, SyncDeltaSequenceNo : :: bincode :: Decode :: decode (decoder) ?,})}} impl < '__de , __Context > :: bincode :: BorrowDecode < '__de , __Context > for CThostFtdcSyncDeltaOptInvstMarginField {fn borrow_decode < __D : :: bincode :: de :: BorrowDecoder <'__de , Context = __Context > > (decoder : & mut __D) ->core :: result :: Result < Self , :: bincode :: error :: DecodeError > {core :: result :: Result :: Ok (Self {InstrumentID : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, InvestorRange : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, BrokerID : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, InvestorID : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, SShortMarginRatioByMoney : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, SShortMarginRatioByVolume : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, HShortMarginRatioByMoney : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, HShortMarginRatioByVolume : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, AShortMarginRatioByMoney : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, AShortMarginRatioByVolume : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, IsRelative : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, MShortMarginRatioByMoney : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, MShortMarginRatioByVolume : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, ActionDirection : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, SyncDeltaSequenceNo : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?,})}}