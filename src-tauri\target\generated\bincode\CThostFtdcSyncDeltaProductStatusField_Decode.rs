impl < __Context > :: bincode :: Decode < __Context > for CThostFtdcSyncDeltaProductStatusField {fn decode < __D : :: bincode :: de :: Decoder < Context = __Context > > (decoder : & mut __D) ->core :: result :: Result < Self , :: bincode :: error :: DecodeError > {core :: result :: Result :: Ok (Self {SyncDeltaSequenceNo : :: bincode :: Decode :: decode (decoder) ?, ExchangeID : :: bincode :: Decode :: decode (decoder) ?, ProductID : :: bincode :: Decode :: decode (decoder) ?, ProductStatus : :: bincode :: Decode :: decode (decoder) ?,})}} impl < '__de , __Context > :: bincode :: BorrowDecode < '__de , __Context > for CThostFtdcSyncDeltaProductStatusField {fn borrow_decode < __D : :: bincode :: de :: BorrowDecoder <'__de , Context = __Context > > (decoder : & mut __D) ->core :: result :: Result < Self , :: bincode :: error :: DecodeError > {core :: result :: Result :: Ok (Self {SyncDeltaSequenceNo : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, ExchangeID : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, ProductID : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, ProductStatus : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?,})}}