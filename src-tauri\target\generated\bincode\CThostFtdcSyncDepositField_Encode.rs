impl :: bincode :: Encode for CThostFtdcSyncDepositField {fn encode < __E : :: bincode :: enc :: Encoder > (& self , encoder : & mut __E) ->core :: result :: Result < () , :: bincode :: error :: EncodeError > {:: bincode :: Encode :: encode (& self . DepositSeqNo , encoder) ?; :: bincode :: Encode :: encode (& self . BrokerID , encoder) ?; :: bincode :: Encode :: encode (& self . InvestorID , encoder) ?; :: bincode :: Encode :: encode (& self . Deposit , encoder) ?; :: bincode :: Encode :: encode (& self . IsForce , encoder) ?; :: bincode :: Encode :: encode (& self . CurrencyID , encoder) ?; :: bincode :: Encode :: encode (& self . IsFromSopt , encoder) ?; :: bincode :: Encode :: encode (& self . TradingPassword , encoder) ?; core :: result :: Result :: Ok (())}}