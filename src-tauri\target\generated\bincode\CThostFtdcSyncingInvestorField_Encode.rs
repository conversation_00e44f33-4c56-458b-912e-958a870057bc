impl :: bincode :: Encode for CThostFtdcSyncingInvestorField {fn encode < __E : :: bincode :: enc :: Encoder > (& self , encoder : & mut __E) ->core :: result :: Result < () , :: bincode :: error :: EncodeError > {:: bincode :: Encode :: encode (& self . InvestorID , encoder) ?; :: bincode :: Encode :: encode (& self . BrokerID , encoder) ?; :: bincode :: Encode :: encode (& self . InvestorGroupID , encoder) ?; :: bincode :: Encode :: encode (& self . InvestorName , encoder) ?; :: bincode :: Encode :: encode (& self . IdentifiedCardType , encoder) ?; :: bincode :: Encode :: encode (& self . IdentifiedCardNo , encoder) ?; :: bincode :: Encode :: encode (& self . IsActive , encoder) ?; :: bincode :: Encode :: encode (& self . Telephone , encoder) ?; :: bincode :: Encode :: encode (& self . Address , encoder) ?; :: bincode :: Encode :: encode (& self . OpenDate , encoder) ?; :: bincode :: Encode :: encode (& self . Mobile , encoder) ?; :: bincode :: Encode :: encode (& self . CommModelID , encoder) ?; :: bincode :: Encode :: encode (& self . MarginModelID , encoder) ?; core :: result :: Result :: Ok (())}}