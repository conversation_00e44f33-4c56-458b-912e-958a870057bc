impl < __Context > :: bincode :: Decode < __Context > for CThostFtdcSyncingInvestorPositionField {fn decode < __D : :: bincode :: de :: Decoder < Context = __Context > > (decoder : & mut __D) ->core :: result :: Result < Self , :: bincode :: error :: DecodeError > {core :: result :: Result :: Ok (Self {reserve1 : :: bincode :: Decode :: decode (decoder) ?, BrokerID : :: bincode :: Decode :: decode (decoder) ?, InvestorID : :: bincode :: Decode :: decode (decoder) ?, PosiDirection : :: bincode :: Decode :: decode (decoder) ?, HedgeFlag : :: bincode :: Decode :: decode (decoder) ?, PositionDate : :: bincode :: Decode :: decode (decoder) ?, YdPosition : :: bincode :: Decode :: decode (decoder) ?, Position : :: bincode :: Decode :: decode (decoder) ?, LongFrozen : :: bincode :: Decode :: decode (decoder) ?, ShortFrozen : :: bincode :: Decode :: decode (decoder) ?, LongFrozenAmount : :: bincode :: Decode :: decode (decoder) ?, ShortFrozenAmount : :: bincode :: Decode :: decode (decoder) ?, OpenVolume : :: bincode :: Decode :: decode (decoder) ?, CloseVolume : :: bincode :: Decode :: decode (decoder) ?, OpenAmount : :: bincode :: Decode :: decode (decoder) ?, CloseAmount : :: bincode :: Decode :: decode (decoder) ?, PositionCost : :: bincode :: Decode :: decode (decoder) ?, PreMargin : :: bincode :: Decode :: decode (decoder) ?, UseMargin : :: bincode :: Decode :: decode (decoder) ?, FrozenMargin : :: bincode :: Decode :: decode (decoder) ?, FrozenCash : :: bincode :: Decode :: decode (decoder) ?, FrozenCommission : :: bincode :: Decode :: decode (decoder) ?, CashIn : :: bincode :: Decode :: decode (decoder) ?, Commission : :: bincode :: Decode :: decode (decoder) ?, CloseProfit : :: bincode :: Decode :: decode (decoder) ?, PositionProfit : :: bincode :: Decode :: decode (decoder) ?, PreSettlementPrice : :: bincode :: Decode :: decode (decoder) ?, SettlementPrice : :: bincode :: Decode :: decode (decoder) ?, TradingDay : :: bincode :: Decode :: decode (decoder) ?, SettlementID : :: bincode :: Decode :: decode (decoder) ?, OpenCost : :: bincode :: Decode :: decode (decoder) ?, ExchangeMargin : :: bincode :: Decode :: decode (decoder) ?, CombPosition : :: bincode :: Decode :: decode (decoder) ?, CombLongFrozen : :: bincode :: Decode :: decode (decoder) ?, CombShortFrozen : :: bincode :: Decode :: decode (decoder) ?, CloseProfitByDate : :: bincode :: Decode :: decode (decoder) ?, CloseProfitByTrade : :: bincode :: Decode :: decode (decoder) ?, TodayPosition : :: bincode :: Decode :: decode (decoder) ?, MarginRateByMoney : :: bincode :: Decode :: decode (decoder) ?, MarginRateByVolume : :: bincode :: Decode :: decode (decoder) ?, StrikeFrozen : :: bincode :: Decode :: decode (decoder) ?, StrikeFrozenAmount : :: bincode :: Decode :: decode (decoder) ?, AbandonFrozen : :: bincode :: Decode :: decode (decoder) ?, ExchangeID : :: bincode :: Decode :: decode (decoder) ?, YdStrikeFrozen : :: bincode :: Decode :: decode (decoder) ?, InvestUnitID : :: bincode :: Decode :: decode (decoder) ?, PositionCostOffset : :: bincode :: Decode :: decode (decoder) ?, TasPosition : :: bincode :: Decode :: decode (decoder) ?, TasPositionCost : :: bincode :: Decode :: decode (decoder) ?, InstrumentID : :: bincode :: Decode :: decode (decoder) ?,})}} impl < '__de , __Context > :: bincode :: BorrowDecode < '__de , __Context > for CThostFtdcSyncingInvestorPositionField {fn borrow_decode < __D : :: bincode :: de :: BorrowDecoder <'__de , Context = __Context > > (decoder : & mut __D) ->core :: result :: Result < Self , :: bincode :: error :: DecodeError > {core :: result :: Result :: Ok (Self {reserve1 : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, BrokerID : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, InvestorID : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, PosiDirection : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, HedgeFlag : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, PositionDate : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, YdPosition : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, Position : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, LongFrozen : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, ShortFrozen : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, LongFrozenAmount : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, ShortFrozenAmount : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, OpenVolume : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, CloseVolume : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, OpenAmount : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, CloseAmount : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, PositionCost : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, PreMargin : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, UseMargin : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, FrozenMargin : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, FrozenCash : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, FrozenCommission : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, CashIn : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, Commission : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, CloseProfit : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, PositionProfit : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, PreSettlementPrice : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, SettlementPrice : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, TradingDay : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, SettlementID : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, OpenCost : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, ExchangeMargin : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, CombPosition : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, CombLongFrozen : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, CombShortFrozen : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, CloseProfitByDate : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, CloseProfitByTrade : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, TodayPosition : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, MarginRateByMoney : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, MarginRateByVolume : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, StrikeFrozen : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, StrikeFrozenAmount : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, AbandonFrozen : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, ExchangeID : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, YdStrikeFrozen : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, InvestUnitID : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, PositionCostOffset : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, TasPosition : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, TasPositionCost : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, InstrumentID : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?,})}}