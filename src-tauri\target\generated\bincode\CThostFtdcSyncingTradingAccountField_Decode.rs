impl < __Context > :: bincode :: Decode < __Context > for CThostFtdcSyncingTradingAccountField {fn decode < __D : :: bincode :: de :: Decoder < Context = __Context > > (decoder : & mut __D) ->core :: result :: Result < Self , :: bincode :: error :: DecodeError > {core :: result :: Result :: Ok (Self {BrokerID : :: bincode :: Decode :: decode (decoder) ?, AccountID : :: bincode :: Decode :: decode (decoder) ?, PreMortgage : :: bincode :: Decode :: decode (decoder) ?, PreCredit : :: bincode :: Decode :: decode (decoder) ?, PreDeposit : :: bincode :: Decode :: decode (decoder) ?, PreBalance : :: bincode :: Decode :: decode (decoder) ?, PreMargin : :: bincode :: Decode :: decode (decoder) ?, InterestBase : :: bincode :: Decode :: decode (decoder) ?, Interest : :: bincode :: Decode :: decode (decoder) ?, Deposit : :: bincode :: Decode :: decode (decoder) ?, Withdraw : :: bincode :: Decode :: decode (decoder) ?, FrozenMargin : :: bincode :: Decode :: decode (decoder) ?, FrozenCash : :: bincode :: Decode :: decode (decoder) ?, FrozenCommission : :: bincode :: Decode :: decode (decoder) ?, CurrMargin : :: bincode :: Decode :: decode (decoder) ?, CashIn : :: bincode :: Decode :: decode (decoder) ?, Commission : :: bincode :: Decode :: decode (decoder) ?, CloseProfit : :: bincode :: Decode :: decode (decoder) ?, PositionProfit : :: bincode :: Decode :: decode (decoder) ?, Balance : :: bincode :: Decode :: decode (decoder) ?, Available : :: bincode :: Decode :: decode (decoder) ?, WithdrawQuota : :: bincode :: Decode :: decode (decoder) ?, Reserve : :: bincode :: Decode :: decode (decoder) ?, TradingDay : :: bincode :: Decode :: decode (decoder) ?, SettlementID : :: bincode :: Decode :: decode (decoder) ?, Credit : :: bincode :: Decode :: decode (decoder) ?, Mortgage : :: bincode :: Decode :: decode (decoder) ?, ExchangeMargin : :: bincode :: Decode :: decode (decoder) ?, DeliveryMargin : :: bincode :: Decode :: decode (decoder) ?, ExchangeDeliveryMargin : :: bincode :: Decode :: decode (decoder) ?, ReserveBalance : :: bincode :: Decode :: decode (decoder) ?, CurrencyID : :: bincode :: Decode :: decode (decoder) ?, PreFundMortgageIn : :: bincode :: Decode :: decode (decoder) ?, PreFundMortgageOut : :: bincode :: Decode :: decode (decoder) ?, FundMortgageIn : :: bincode :: Decode :: decode (decoder) ?, FundMortgageOut : :: bincode :: Decode :: decode (decoder) ?, FundMortgageAvailable : :: bincode :: Decode :: decode (decoder) ?, MortgageableFund : :: bincode :: Decode :: decode (decoder) ?, SpecProductMargin : :: bincode :: Decode :: decode (decoder) ?, SpecProductFrozenMargin : :: bincode :: Decode :: decode (decoder) ?, SpecProductCommission : :: bincode :: Decode :: decode (decoder) ?, SpecProductFrozenCommission : :: bincode :: Decode :: decode (decoder) ?, SpecProductPositionProfit : :: bincode :: Decode :: decode (decoder) ?, SpecProductCloseProfit : :: bincode :: Decode :: decode (decoder) ?, SpecProductPositionProfitByAlg : :: bincode :: Decode :: decode (decoder) ?, SpecProductExchangeMargin : :: bincode :: Decode :: decode (decoder) ?, FrozenSwap : :: bincode :: Decode :: decode (decoder) ?, RemainSwap : :: bincode :: Decode :: decode (decoder) ?,})}} impl < '__de , __Context > :: bincode :: BorrowDecode < '__de , __Context > for CThostFtdcSyncingTradingAccountField {fn borrow_decode < __D : :: bincode :: de :: BorrowDecoder <'__de , Context = __Context > > (decoder : & mut __D) ->core :: result :: Result < Self , :: bincode :: error :: DecodeError > {core :: result :: Result :: Ok (Self {BrokerID : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, AccountID : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, PreMortgage : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, PreCredit : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, PreDeposit : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, PreBalance : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, PreMargin : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, InterestBase : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, Interest : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, Deposit : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, Withdraw : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, FrozenMargin : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, FrozenCash : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, FrozenCommission : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, CurrMargin : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, CashIn : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, Commission : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, CloseProfit : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, PositionProfit : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, Balance : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, Available : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, WithdrawQuota : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, Reserve : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, TradingDay : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, SettlementID : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, Credit : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, Mortgage : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, ExchangeMargin : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, DeliveryMargin : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, ExchangeDeliveryMargin : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, ReserveBalance : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, CurrencyID : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, PreFundMortgageIn : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, PreFundMortgageOut : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, FundMortgageIn : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, FundMortgageOut : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, FundMortgageAvailable : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, MortgageableFund : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, SpecProductMargin : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, SpecProductFrozenMargin : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, SpecProductCommission : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, SpecProductFrozenCommission : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, SpecProductPositionProfit : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, SpecProductCloseProfit : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, SpecProductPositionProfitByAlg : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, SpecProductExchangeMargin : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, FrozenSwap : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, RemainSwap : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?,})}}