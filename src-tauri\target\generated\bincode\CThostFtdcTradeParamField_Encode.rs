impl :: bincode :: Encode for CThostFtdcTradeParamField {fn encode < __E : :: bincode :: enc :: Encoder > (& self , encoder : & mut __E) ->core :: result :: Result < () , :: bincode :: error :: EncodeError > {:: bincode :: Encode :: encode (& self . BrokerID , encoder) ?; :: bincode :: Encode :: encode (& self . TradeParamID , encoder) ?; :: bincode :: Encode :: encode (& self . TradeParamValue , encoder) ?; :: bincode :: Encode :: encode (& self . Memo , encoder) ?; core :: result :: Result :: Ok (())}}