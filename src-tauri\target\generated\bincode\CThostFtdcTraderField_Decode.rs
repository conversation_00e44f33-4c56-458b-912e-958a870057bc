impl < __Context > :: bincode :: Decode < __Context > for CThostFtdcTraderField {fn decode < __D : :: bincode :: de :: Decoder < Context = __Context > > (decoder : & mut __D) ->core :: result :: Result < Self , :: bincode :: error :: DecodeError > {core :: result :: Result :: Ok (Self {ExchangeID : :: bincode :: Decode :: decode (decoder) ?, TraderID : :: bincode :: Decode :: decode (decoder) ?, ParticipantID : :: bincode :: Decode :: decode (decoder) ?, Password : :: bincode :: Decode :: decode (decoder) ?, InstallCount : :: bincode :: Decode :: decode (decoder) ?, BrokerID : :: bincode :: Decode :: decode (decoder) ?, OrderCancelAlg : :: bincode :: Decode :: decode (decoder) ?,})}} impl < '__de , __Context > :: bincode :: BorrowDecode < '__de , __Context > for CThostFtdcTraderField {fn borrow_decode < __D : :: bincode :: de :: BorrowDecoder <'__de , Context = __Context > > (decoder : & mut __D) ->core :: result :: Result < Self , :: bincode :: error :: DecodeError > {core :: result :: Result :: Ok (Self {ExchangeID : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, TraderID : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, ParticipantID : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, Password : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, InstallCount : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, BrokerID : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, OrderCancelAlg : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?,})}}