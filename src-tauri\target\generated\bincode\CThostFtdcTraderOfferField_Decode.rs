impl < __Context > :: bincode :: Decode < __Context > for CThostFtdcTraderOfferField {fn decode < __D : :: bincode :: de :: Decoder < Context = __Context > > (decoder : & mut __D) ->core :: result :: Result < Self , :: bincode :: error :: DecodeError > {core :: result :: Result :: Ok (Self {ExchangeID : :: bincode :: Decode :: decode (decoder) ?, TraderID : :: bincode :: Decode :: decode (decoder) ?, ParticipantID : :: bincode :: Decode :: decode (decoder) ?, Password : :: bincode :: Decode :: decode (decoder) ?, InstallID : :: bincode :: Decode :: decode (decoder) ?, OrderLocalID : :: bincode :: Decode :: decode (decoder) ?, TraderConnectStatus : :: bincode :: Decode :: decode (decoder) ?, ConnectRequestDate : :: bincode :: Decode :: decode (decoder) ?, ConnectRequestTime : :: bincode :: Decode :: decode (decoder) ?, LastReportDate : :: bincode :: Decode :: decode (decoder) ?, LastReportTime : :: bincode :: Decode :: decode (decoder) ?, ConnectDate : :: bincode :: Decode :: decode (decoder) ?, ConnectTime : :: bincode :: Decode :: decode (decoder) ?, StartDate : :: bincode :: Decode :: decode (decoder) ?, StartTime : :: bincode :: Decode :: decode (decoder) ?, TradingDay : :: bincode :: Decode :: decode (decoder) ?, BrokerID : :: bincode :: Decode :: decode (decoder) ?, MaxTradeID : :: bincode :: Decode :: decode (decoder) ?, MaxOrderMessageReference : :: bincode :: Decode :: decode (decoder) ?, OrderCancelAlg : :: bincode :: Decode :: decode (decoder) ?,})}} impl < '__de , __Context > :: bincode :: BorrowDecode < '__de , __Context > for CThostFtdcTraderOfferField {fn borrow_decode < __D : :: bincode :: de :: BorrowDecoder <'__de , Context = __Context > > (decoder : & mut __D) ->core :: result :: Result < Self , :: bincode :: error :: DecodeError > {core :: result :: Result :: Ok (Self {ExchangeID : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, TraderID : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, ParticipantID : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, Password : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, InstallID : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, OrderLocalID : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, TraderConnectStatus : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, ConnectRequestDate : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, ConnectRequestTime : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, LastReportDate : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, LastReportTime : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, ConnectDate : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, ConnectTime : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, StartDate : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, StartTime : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, TradingDay : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, BrokerID : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, MaxTradeID : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, MaxOrderMessageReference : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, OrderCancelAlg : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?,})}}