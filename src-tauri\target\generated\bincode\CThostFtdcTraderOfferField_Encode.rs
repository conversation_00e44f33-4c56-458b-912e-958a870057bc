impl :: bincode :: Encode for CThostFtdcTrader<PERSON>fferField {fn encode < __E : :: bincode :: enc :: Encoder > (& self , encoder : & mut __E) ->core :: result :: Result < () , :: bincode :: error :: EncodeError > {:: bincode :: Encode :: encode (& self . ExchangeID , encoder) ?; :: bincode :: Encode :: encode (& self . TraderID , encoder) ?; :: bincode :: Encode :: encode (& self . ParticipantID , encoder) ?; :: bincode :: Encode :: encode (& self . Password , encoder) ?; :: bincode :: Encode :: encode (& self . InstallID , encoder) ?; :: bincode :: Encode :: encode (& self . OrderLocalID , encoder) ?; :: bincode :: Encode :: encode (& self . TraderConnectStatus , encoder) ?; :: bincode :: Encode :: encode (& self . ConnectRequestDate , encoder) ?; :: bincode :: Encode :: encode (& self . ConnectRequestTime , encoder) ?; :: bincode :: Encode :: encode (& self . LastReportDate , encoder) ?; :: bincode :: Encode :: encode (& self . LastReportTime , encoder) ?; :: bincode :: Encode :: encode (& self . ConnectDate , encoder) ?; :: bincode :: Encode :: encode (& self . ConnectTime , encoder) ?; :: bincode :: Encode :: encode (& self . StartDate , encoder) ?; :: bincode :: Encode :: encode (& self . StartTime , encoder) ?; :: bincode :: Encode :: encode (& self . TradingDay , encoder) ?; :: bincode :: Encode :: encode (& self . BrokerID , encoder) ?; :: bincode :: Encode :: encode (& self . MaxTradeID , encoder) ?; :: bincode :: Encode :: encode (& self . MaxOrderMessageReference , encoder) ?; :: bincode :: Encode :: encode (& self . OrderCancelAlg , encoder) ?; core :: result :: Result :: Ok (())}}