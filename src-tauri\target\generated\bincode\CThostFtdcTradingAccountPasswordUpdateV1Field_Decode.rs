impl < __Context > :: bincode :: Decode < __Context > for CThostFtdcTradingAccountPasswordUpdateV1Field {fn decode < __D : :: bincode :: de :: Decoder < Context = __Context > > (decoder : & mut __D) ->core :: result :: Result < Self , :: bincode :: error :: DecodeError > {core :: result :: Result :: Ok (Self {BrokerID : :: bincode :: Decode :: decode (decoder) ?, InvestorID : :: bincode :: Decode :: decode (decoder) ?, OldPassword : :: bincode :: Decode :: decode (decoder) ?, NewPassword : :: bincode :: Decode :: decode (decoder) ?,})}} impl < '__de , __Context > :: bincode :: BorrowDecode < '__de , __Context > for CThostFtdcTradingAccountPasswordUpdateV1Field {fn borrow_decode < __D : :: bincode :: de :: BorrowDecoder <'__de , Context = __Context > > (decoder : & mut __D) ->core :: result :: Result < Self , :: bincode :: error :: DecodeError > {core :: result :: Result :: Ok (Self {BrokerID : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, InvestorID : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, OldPassword : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, NewPassword : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?,})}}