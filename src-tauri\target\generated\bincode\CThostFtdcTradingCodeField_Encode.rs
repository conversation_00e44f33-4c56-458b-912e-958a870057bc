impl :: bincode :: Encode for CThostFtdcTradingCodeField {fn encode < __E : :: bincode :: enc :: Encoder > (& self , encoder : & mut __E) ->core :: result :: Result < () , :: bincode :: error :: EncodeError > {:: bincode :: Encode :: encode (& self . InvestorID , encoder) ?; :: bincode :: Encode :: encode (& self . BrokerID , encoder) ?; :: bincode :: Encode :: encode (& self . ExchangeID , encoder) ?; :: bincode :: Encode :: encode (& self . ClientID , encoder) ?; :: bincode :: Encode :: encode (& self . IsActive , encoder) ?; :: bincode :: Encode :: encode (& self . ClientIDType , encoder) ?; :: bincode :: Encode :: encode (& self . BranchID , encoder) ?; :: bincode :: Encode :: encode (& self . BizType , encoder) ?; :: bincode :: Encode :: encode (& self . InvestUnitID , encoder) ?; core :: result :: Result :: Ok (())}}