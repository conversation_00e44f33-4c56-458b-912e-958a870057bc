impl :: bincode :: Encode for CThostFtdcTradingNoticeInfoField {fn encode < __E : :: bincode :: enc :: Encoder > (& self , encoder : & mut __E) ->core :: result :: Result < () , :: bincode :: error :: EncodeError > {:: bincode :: Encode :: encode (& self . BrokerID , encoder) ?; :: bincode :: Encode :: encode (& self . InvestorID , encoder) ?; :: bincode :: Encode :: encode (& self . SendTime , encoder) ?; :: bincode :: Encode :: encode (& self . FieldContent , encoder) ?; :: bincode :: Encode :: encode (& self . SequenceSeries , encoder) ?; :: bincode :: Encode :: encode (& self . SequenceNo , encoder) ?; :: bincode :: Encode :: encode (& self . InvestUnitID , encoder) ?; core :: result :: Result :: Ok (())}}