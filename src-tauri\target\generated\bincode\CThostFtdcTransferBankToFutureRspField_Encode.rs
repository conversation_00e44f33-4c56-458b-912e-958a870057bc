impl :: bincode :: Encode for CThostFtdcTransferBankToFutureRspField {fn encode < __E : :: bincode :: enc :: Encoder > (& self , encoder : & mut __E) ->core :: result :: Result < () , :: bincode :: error :: EncodeError > {:: bincode :: Encode :: encode (& self . RetCode , encoder) ?; :: bincode :: Encode :: encode (& self . RetInfo , encoder) ?; :: bincode :: Encode :: encode (& self . FutureAccount , encoder) ?; :: bincode :: Encode :: encode (& self . TradeAmt , encoder) ?; :: bincode :: Encode :: encode (& self . <PERSON><PERSON>Fee , encoder) ?; :: bincode :: Encode :: encode (& self . CurrencyCode , encoder) ?; core :: result :: Result :: Ok (())}}