impl < __Context > :: bincode :: Decode < __Context > for CThostFtdcTransferFutureToBankRspField {fn decode < __D : :: bincode :: de :: Decoder < Context = __Context > > (decoder : & mut __D) ->core :: result :: Result < Self , :: bincode :: error :: DecodeError > {core :: result :: Result :: Ok (Self {RetCode : :: bincode :: Decode :: decode (decoder) ?, RetInfo : :: bincode :: Decode :: decode (decoder) ?, FutureAccount : :: bincode :: Decode :: decode (decoder) ?, TradeAmt : :: bincode :: Decode :: decode (decoder) ?, CustFee : :: bincode :: Decode :: decode (decoder) ?, CurrencyCode : :: bincode :: Decode :: decode (decoder) ?,})}} impl < '__de , __Context > :: bincode :: BorrowDecode < '__de , __Context > for CThostFtdcTransferFutureToBankRspField {fn borrow_decode < __D : :: bincode :: de :: BorrowDecoder <'__de , Context = __Context > > (decoder : & mut __D) ->core :: result :: Result < Self , :: bincode :: error :: DecodeError > {core :: result :: Result :: Ok (Self {RetCode : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, RetInfo : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, FutureAccount : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, TradeAmt : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, CustFee : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, CurrencyCode : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?,})}}