impl < __Context > :: bincode :: Decode < __Context > for CThostFtdcTransferQryBankReqField {fn decode < __D : :: bincode :: de :: Decoder < Context = __Context > > (decoder : & mut __D) ->core :: result :: Result < Self , :: bincode :: error :: DecodeError > {core :: result :: Result :: Ok (Self {FutureAccount : :: bincode :: Decode :: decode (decoder) ?, FuturePwdFlag : :: bincode :: Decode :: decode (decoder) ?, FutureAccPwd : :: bincode :: Decode :: decode (decoder) ?, CurrencyCode : :: bincode :: Decode :: decode (decoder) ?,})}} impl < '__de , __Context > :: bincode :: BorrowDecode < '__de , __Context > for CThostFtdcTransferQryBankReqField {fn borrow_decode < __D : :: bincode :: de :: BorrowDecoder <'__de , Context = __Context > > (decoder : & mut __D) ->core :: result :: Result < Self , :: bincode :: error :: DecodeError > {core :: result :: Result :: Ok (Self {FutureAccount : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, FuturePwdFlag : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, FutureAccPwd : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, CurrencyCode : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?,})}}