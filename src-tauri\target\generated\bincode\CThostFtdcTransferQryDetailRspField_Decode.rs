impl < __Context > :: bincode :: Decode < __Context > for CThostFtdcTransferQryDetailRspField {fn decode < __D : :: bincode :: de :: Decoder < Context = __Context > > (decoder : & mut __D) ->core :: result :: Result < Self , :: bincode :: error :: DecodeError > {core :: result :: Result :: Ok (Self {TradeDate : :: bincode :: Decode :: decode (decoder) ?, TradeTime : :: bincode :: Decode :: decode (decoder) ?, TradeCode : :: bincode :: Decode :: decode (decoder) ?, FutureSerial : :: bincode :: Decode :: decode (decoder) ?, FutureID : :: bincode :: Decode :: decode (decoder) ?, FutureAccount : :: bincode :: Decode :: decode (decoder) ?, BankSerial : :: bincode :: Decode :: decode (decoder) ?, BankID : :: bincode :: Decode :: decode (decoder) ?, BankBrchID : :: bincode :: Decode :: decode (decoder) ?, BankAccount : :: bincode :: Decode :: decode (decoder) ?, CertCode : :: bincode :: Decode :: decode (decoder) ?, CurrencyCode : :: bincode :: Decode :: decode (decoder) ?, TxAmount : :: bincode :: Decode :: decode (decoder) ?, Flag : :: bincode :: Decode :: decode (decoder) ?,})}} impl < '__de , __Context > :: bincode :: BorrowDecode < '__de , __Context > for CThostFtdcTransferQryDetailRspField {fn borrow_decode < __D : :: bincode :: de :: BorrowDecoder <'__de , Context = __Context > > (decoder : & mut __D) ->core :: result :: Result < Self , :: bincode :: error :: DecodeError > {core :: result :: Result :: Ok (Self {TradeDate : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, TradeTime : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, TradeCode : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, FutureSerial : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, FutureID : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, FutureAccount : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, BankSerial : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, BankID : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, BankBrchID : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, BankAccount : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, CertCode : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, CurrencyCode : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, TxAmount : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, Flag : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?,})}}