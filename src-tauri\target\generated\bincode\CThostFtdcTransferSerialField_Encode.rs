impl :: bincode :: Encode for CThostFtdcTransferSerialField {fn encode < __E : :: bincode :: enc :: Encoder > (& self , encoder : & mut __E) ->core :: result :: Result < () , :: bincode :: error :: EncodeError > {:: bincode :: Encode :: encode (& self . PlateSerial , encoder) ?; :: bincode :: Encode :: encode (& self . TradeDate , encoder) ?; :: bincode :: Encode :: encode (& self . TradingDay , encoder) ?; :: bincode :: Encode :: encode (& self . TradeTime , encoder) ?; :: bincode :: Encode :: encode (& self . TradeCode , encoder) ?; :: bincode :: Encode :: encode (& self . SessionID , encoder) ?; :: bincode :: Encode :: encode (& self . BankID , encoder) ?; :: bincode :: Encode :: encode (& self . BankBranchID , encoder) ?; :: bincode :: Encode :: encode (& self . BankAccType , encoder) ?; :: bincode :: Encode :: encode (& self . BankAccount , encoder) ?; :: bincode :: Encode :: encode (& self . BankSerial , encoder) ?; :: bincode :: Encode :: encode (& self . BrokerID , encoder) ?; :: bincode :: Encode :: encode (& self . BrokerBranchID , encoder) ?; :: bincode :: Encode :: encode (& self . FutureAccType , encoder) ?; :: bincode :: Encode :: encode (& self . AccountID , encoder) ?; :: bincode :: Encode :: encode (& self . InvestorID , encoder) ?; :: bincode :: Encode :: encode (& self . FutureSerial , encoder) ?; :: bincode :: Encode :: encode (& self . IdCardType , encoder) ?; :: bincode :: Encode :: encode (& self . IdentifiedCardNo , encoder) ?; :: bincode :: Encode :: encode (& self . CurrencyID , encoder) ?; :: bincode :: Encode :: encode (& self . TradeAmount , encoder) ?; :: bincode :: Encode :: encode (& self . CustFee , encoder) ?; :: bincode :: Encode :: encode (& self . BrokerFee , encoder) ?; :: bincode :: Encode :: encode (& self . AvailabilityFlag , encoder) ?; :: bincode :: Encode :: encode (& self . OperatorCode , encoder) ?; :: bincode :: Encode :: encode (& self . BankNewAccount , encoder) ?; :: bincode :: Encode :: encode (& self . ErrorID , encoder) ?; :: bincode :: Encode :: encode (& self . ErrorMsg , encoder) ?; core :: result :: Result :: Ok (())}}