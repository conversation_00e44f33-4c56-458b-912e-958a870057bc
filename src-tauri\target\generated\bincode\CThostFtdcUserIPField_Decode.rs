impl < __Context > :: bincode :: Decode < __Context > for CThostFtdcUserIPField {fn decode < __D : :: bincode :: de :: Decoder < Context = __Context > > (decoder : & mut __D) ->core :: result :: Result < Self , :: bincode :: error :: DecodeError > {core :: result :: Result :: Ok (Self {BrokerID : :: bincode :: Decode :: decode (decoder) ?, UserID : :: bincode :: Decode :: decode (decoder) ?, reserve1 : :: bincode :: Decode :: decode (decoder) ?, reserve2 : :: bincode :: Decode :: decode (decoder) ?, MacAddress : :: bincode :: Decode :: decode (decoder) ?, IPAddress : :: bincode :: Decode :: decode (decoder) ?, IPMask : :: bincode :: Decode :: decode (decoder) ?,})}} impl < '__de , __Context > :: bincode :: BorrowDecode < '__de , __Context > for CThostFtdcUserIPField {fn borrow_decode < __D : :: bincode :: de :: BorrowDecoder <'__de , Context = __Context > > (decoder : & mut __D) ->core :: result :: Result < Self , :: bincode :: error :: DecodeError > {core :: result :: Result :: Ok (Self {BrokerID : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, UserID : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, reserve1 : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, reserve2 : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, MacAddress : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, IPAddress : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, IPMask : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?,})}}