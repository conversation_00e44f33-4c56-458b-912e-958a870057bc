impl < __Context > :: bincode :: Decode < __Context > for CThostFtdcUserSessionField {fn decode < __D : :: bincode :: de :: Decoder < Context = __Context > > (decoder : & mut __D) ->core :: result :: Result < Self , :: bincode :: error :: DecodeError > {core :: result :: Result :: Ok (Self {FrontID : :: bincode :: Decode :: decode (decoder) ?, SessionID : :: bincode :: Decode :: decode (decoder) ?, BrokerID : :: bincode :: Decode :: decode (decoder) ?, UserID : :: bincode :: Decode :: decode (decoder) ?, LoginDate : :: bincode :: Decode :: decode (decoder) ?, LoginTime : :: bincode :: Decode :: decode (decoder) ?, reserve1 : :: bincode :: Decode :: decode (decoder) ?, UserProductInfo : :: bincode :: Decode :: decode (decoder) ?, InterfaceProductInfo : :: bincode :: Decode :: decode (decoder) ?, ProtocolInfo : :: bincode :: Decode :: decode (decoder) ?, MacAddress : :: bincode :: Decode :: decode (decoder) ?, LoginRemark : :: bincode :: Decode :: decode (decoder) ?, IPAddress : :: bincode :: Decode :: decode (decoder) ?,})}} impl < '__de , __Context > :: bincode :: BorrowDecode < '__de , __Context > for CThostFtdcUserSessionField {fn borrow_decode < __D : :: bincode :: de :: BorrowDecoder <'__de , Context = __Context > > (decoder : & mut __D) ->core :: result :: Result < Self , :: bincode :: error :: DecodeError > {core :: result :: Result :: Ok (Self {FrontID : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, SessionID : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, BrokerID : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, UserID : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, LoginDate : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, LoginTime : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, reserve1 : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, UserProductInfo : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, InterfaceProductInfo : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, ProtocolInfo : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, MacAddress : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, LoginRemark : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, IPAddress : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?,})}}