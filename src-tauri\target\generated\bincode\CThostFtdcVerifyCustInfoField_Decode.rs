impl < __Context > :: bincode :: Decode < __Context > for CThostFtdcVerifyCustInfoField {fn decode < __D : :: bincode :: de :: Decoder < Context = __Context > > (decoder : & mut __D) ->core :: result :: Result < Self , :: bincode :: error :: DecodeError > {core :: result :: Result :: Ok (Self {CustomerName : :: bincode :: Decode :: decode (decoder) ?, IdCardType : :: bincode :: Decode :: decode (decoder) ?, IdentifiedCardNo : :: bincode :: Decode :: decode (decoder) ?, CustType : :: bincode :: Decode :: decode (decoder) ?, LongCustomerName : :: bincode :: Decode :: decode (decoder) ?,})}} impl < '__de , __Context > :: bincode :: BorrowDecode < '__de , __Context > for CThostFtdcVerifyCustInfoField {fn borrow_decode < __D : :: bincode :: de :: BorrowDecoder <'__de , Context = __Context > > (decoder : & mut __D) ->core :: result :: Result < Self , :: bincode :: error :: DecodeError > {core :: result :: Result :: Ok (Self {CustomerName : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, IdCardType : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, IdentifiedCardNo : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, CustType : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?, LongCustomerName : :: bincode :: BorrowDecode ::<'_ , __Context >:: borrow_decode (decoder) ?,})}}