# 自定义标题栏组件 (TitleBar)

一个功能完整的自定义标题栏组件，专为 Tauri 应用程序设计。

## 🚀 特性

- ✅ **完整的窗口控制** - 最小化、最大化、关闭功能
- ✅ **主题系统** - 6种预定义主题 + 自定义主题支持
- ✅ **自定义按钮** - 支持动态添加/移除自定义按钮
- ✅ **插槽支持** - 灵活的内容定制
- ✅ **事件系统** - 完整的事件监听和处理
- ✅ **响应式设计** - 适配不同屏幕尺寸
- ✅ **TypeScript** - 完整的类型定义
- ✅ **无障碍访问** - 支持键盘导航和屏幕阅读器

## 📁 文件结构

```
src/components/
├── TitleBar.vue              # 主组件
├── TitleBarExample.vue       # 使用示例
├── TitleBarUsage.md         # 详细使用文档
└── TitleBar.README.md       # 本文件

src/types/
└── titleBar.ts              # TypeScript 类型定义

src/utils/
└── titleBarUtils.ts         # 工具函数和管理器

src/views/
└── TitleBarDemo.vue         # 完整演示页面
```

## 🎯 快速开始

### 1. 基础使用

```vue
<template>
  <TitleBar title="我的应用" />
</template>

<script setup>
import TitleBar from '@/components/TitleBar.vue'
</script>
```

### 2. 自定义主题

```vue
<template>
  <TitleBar 
    title="自定义主题"
    theme="primary"
  />
</template>
```

### 3. 添加自定义按钮

```vue
<template>
  <TitleBar 
    title="带按钮的标题栏"
    :custom-buttons="buttons"
  />
</template>

<script setup>
const buttons = [
  {
    id: 'settings',
    icon: '⚙️',
    tooltip: '设置',
    onClick: () => console.log('设置')
  }
]
</script>
```

## 🎨 主题

### 预定义主题
- `dark` - 深色主题（默认）
- `light` - 浅色主题
- `primary` - 主色调主题
- `success` - 成功主题
- `warning` - 警告主题
- `danger` - 危险主题

### 自定义主题

```vue
<template>
  <TitleBar 
    :theme="customTheme"
    title="自定义主题"
  />
</template>

<script setup>
const customTheme = {
  backgroundColor: '#6366f1',
  textColor: '#ffffff',
  height: '40px',
  borderColor: 'rgba(255, 255, 255, 0.2)'
}
</script>
```

## 🔧 配置选项

| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `title` | `string` | `'Tauri App'` | 窗口标题 |
| `theme` | `string \| object` | `'dark'` | 主题配置 |
| `showIcon` | `boolean` | `true` | 显示图标 |
| `showControls` | `boolean` | `true` | 显示控制按钮 |
| `customButtons` | `array` | `[]` | 自定义按钮 |
| `enableDoubleClickMaximize` | `boolean` | `true` | 双击最大化 |

## 📡 事件

| 事件名 | 参数 | 说明 |
|--------|------|------|
| `minimize` | - | 窗口最小化 |
| `maximize` | - | 窗口最大化 |
| `restore` | - | 窗口还原 |
| `close` | - | 窗口关闭 |
| `customButtonClick` | `button` | 自定义按钮点击 |

## 🎪 演示

访问 `/title-bar-demo` 路由查看完整的交互式演示。

## 🛠️ 开发

### 依赖要求

- Vue 3.x
- TypeScript
- Tauri 2.x
- Ant Design Vue (可选，仅演示页面使用)

### 配置要求

确保在 `tauri.conf.json` 中设置：

```json
{
  "app": {
    "windows": [
      {
        "decorations": false
      }
    ]
  }
}
```

## 🔍 API 参考

### CustomButton 接口

```typescript
interface CustomButton {
  id: string                    // 唯一标识
  icon?: string                 // 图标 HTML
  text?: string                 // 按钮文本
  tooltip?: string              // 提示文本
  onClick: () => void           // 点击事件
  disabled?: boolean            // 是否禁用
  visible?: boolean             // 是否可见
  style?: Record<string, string> // 自定义样式
  className?: string            // CSS 类名
}
```

### TitleBarTheme 接口

```typescript
interface TitleBarTheme {
  backgroundColor: string       // 背景颜色
  textColor: string            // 文字颜色
  height: string               // 高度
  borderColor?: string         // 边框颜色
  hoverColor?: string          // 悬停颜色
  activeColor?: string         // 激活颜色
}
```

## 🎯 最佳实践

1. **性能优化** - 避免在标题栏中放置复杂组件
2. **用户体验** - 保持标题栏简洁，重要功能优先
3. **主题一致性** - 在整个应用中保持主题统一
4. **事件处理** - 合理处理窗口事件，避免意外操作
5. **响应式设计** - 考虑不同屏幕尺寸的显示效果

## 🐛 故障排除

### 常见问题

1. **拖拽不工作** - 确保设置了 `decorations: false`
2. **按钮点击无响应** - 检查 `pointer-events` CSS 属性
3. **主题不生效** - 验证主题对象格式是否正确
4. **窗口控制失败** - 确保 Tauri API 权限配置正确

### 调试技巧

1. 使用浏览器开发者工具检查 CSS 变量
2. 查看控制台错误信息
3. 使用演示页面测试功能
4. 检查 Tauri 配置文件

## 📄 许可证

本组件遵循项目的整体许可证。

## 🤝 贡献

欢迎提交 Issue 和 Pull Request！

---

**注意**: 这是一个为 Tauri 应用程序专门设计的组件，需要在 Tauri 环境中运行。
