<template>
  <div class="contract-search-window">
    <!-- 搜索框 -->
    <div class="search-container">
      <Input
        v-model:value="searchKeyword"
        placeholder="搜索合约代码或名称..."
        size="large"
        class="search-input"
        @input="handleSearch"
        :disabled="isLoading"
      >
        <template #prefix>
          <SearchOutlined />
        </template>
      </Input>

      <!-- 统计信息和快速过滤 -->
      <div class="search-stats">
        <div class="quick-filters">
          <span class="filter-label">快速筛选：</span>
          <span
            class="filter-tag"
            :class="{ active: !quickFilter }"
            @click="setQuickFilter('')"
          >
            全部
          </span>
          <span
            v-for="category in popularCategories"
            :key="category.code"
            class="filter-tag"
            :class="{ active: quickFilter === category.code }"
            @click="setQuickFilter(category.code)"
          >
            {{ category.name }}
          </span>
        </div>
        <div class="stats-info">
          <span v-if="!isLoading" class="stats-text">
            {{ searchKeyword ? `找到 ${filteredContractsCount} 个合约` : `共 ${totalContractsCount} 个合约` }}
          </span>
          <span v-else class="stats-text loading">
            <LoadingOutlined /> 正在加载合约数据...
          </span>
        </div>
      </div>
    </div>

    <!-- 合约列表 -->
    <div class="contract-list-container">
      <div class="contract-header">
        <div
          class="header-item contract-code sortable"
          :class="{ active: sortBy === 'code' }"
          @click="handleSort('code')"
        >
          合约代码
          <span v-if="sortBy === 'code'" class="sort-icon">
            {{ sortOrder === 'asc' ? '↑' : '↓' }}
          </span>
        </div>
        <div
          class="header-item contract-name sortable"
          :class="{ active: sortBy === 'name' }"
          @click="handleSort('name')"
        >
          合约名称
          <span v-if="sortBy === 'name'" class="sort-icon">
            {{ sortOrder === 'asc' ? '↑' : '↓' }}
          </span>
        </div>
      </div>

      <div class="contract-content">
      
        <!-- 按分类显示合约 -->
        <div
          v-for="category in filteredCategories"
          :key="category.code"
          class="contract-category"
        >
          <div class="category-header">
            <span class="category-name">{{ category.name }}({{ category.code }})</span>
            <span class="category-count">{{ category.contracts.length }}个合约</span>
          </div>

          <div class="category-contracts">
            <div
              v-for="contract in category.contracts"
              :key="contract.code"
              class="contract-item"
              :class="{ 'selected': selectedContract?.code === contract.code }"
              @click="selectContract(contract)"
              @dblclick="openContractPanel(contract)"
            >
              <div class="contract-code">{{ contract.code }}</div>
              <div class="contract-name">{{ contract.name }}</div>
            </div>
          </div>
        </div>

        <!-- 无搜索结果 -->
        <div v-if="filteredCategories.length === 0" class="no-results">
          <Empty description="未找到匹配的合约" />
        </div>
      </div>
    </div>
  </div>

  <!-- 重连对话框 -->
  <ReconnectDialog
    v-model:visible="showReconnectDialog"
    @reconnected="handleReconnected"
  />
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { SearchOutlined, LoadingOutlined } from '@ant-design/icons-vue'
import { Empty, Input, message } from 'ant-design-vue'
import { emit } from '@tauri-apps/api/event'
import { getCurrentWindow } from '@tauri-apps/api/window'
import type { ContractInfo, ContractCategory } from '@/types/trading'
import { useContractStore } from '@/stores/contractStore'
import { contractService } from '@/services/contractService'
import ReconnectDialog from '@/components/ReconnectDialog.vue'

// 响应式数据
const searchKeyword = ref('')
const selectedContract = ref<ContractInfo | null>(null)
const allCategories = ref<ContractCategory[]>([])
const priceUpdateInterval = ref<number | null>(null)
const showReconnectDialog = ref(false)
const isLoading = ref(false)
const totalContractsCount = ref(0)
const sortBy = ref<'code' | 'name' | 'price' | 'change'>('code')
const sortOrder = ref<'asc' | 'desc'>('asc')
const quickFilter = ref('')

// 动态获取的品种分类（从CTP数据中提取）
const popularCategories = computed(() => {
  // 从已加载的分类中提取前10个作为快速筛选
  return allCategories.value.slice(0, 10).map(category => ({
    code: category.code,
    name: category.name
  }))
})

// 合约状态管理
const { setCurrentContract } = useContractStore()


// 计算属性
const filteredCategories = computed(() => {
  let categories = allCategories.value

  // 快速过滤
  if (quickFilter.value) {
    categories = categories.filter(category =>
      category.code.toUpperCase() === quickFilter.value.toUpperCase()
    )
  }

  // 搜索过滤
  if (searchKeyword.value.trim()) {
    const keyword = searchKeyword.value.toLowerCase().trim()

    categories = categories
      .map(category => ({
        ...category,
        contracts: category.contracts.filter(contract =>
          contract.code.toLowerCase().includes(keyword) ||
          contract.name.toLowerCase().includes(keyword) ||
          contract.category.toLowerCase().includes(keyword) ||
          contract.categoryCode.toLowerCase().includes(keyword) ||
          contract.fullCode.toLowerCase().includes(keyword)
        )
      }))
      .filter(category => category.contracts.length > 0)
  }

  // 排序
  return categories.map(category => ({
    ...category,
    contracts: sortContracts(category.contracts)
  }))
})

// 计算过滤后的合约总数
const filteredContractsCount = computed(() => {
  return filteredCategories.value.reduce((total, category) => {
    return total + category.contracts.length
  }, 0)
})

// 排序合约列表
const sortContracts = (contracts: ContractInfo[]) => {
  return [...contracts].sort((a, b) => {
    let aValue: any, bValue: any

    switch (sortBy.value) {
      case 'code':
        aValue = a.code
        bValue = b.code
        break
      case 'name':
        aValue = a.name
        bValue = b.name
        break
      case 'price':
        aValue = a.lastPrice || 0
        bValue = b.lastPrice || 0
        break
      case 'change':
        aValue = a.changePercent || 0
        bValue = b.changePercent || 0
        break
      default:
        return 0
    }

    if (typeof aValue === 'string') {
      const result = aValue.localeCompare(bValue)
      return sortOrder.value === 'asc' ? result : -result
    } else {
      const result = aValue - bValue
      return sortOrder.value === 'asc' ? result : -result
    }
  })
}

// 处理排序
const handleSort = (field: 'code' | 'name' | 'price' | 'change') => {
  if (sortBy.value === field) {
    // 如果点击的是当前排序字段，切换排序方向
    sortOrder.value = sortOrder.value === 'asc' ? 'desc' : 'asc'
  } else {
    // 如果点击的是新字段，设置为升序
    sortBy.value = field
    sortOrder.value = 'asc'
  }
}

// 设置快速过滤
const setQuickFilter = (categoryCode: string) => {
  quickFilter.value = categoryCode
  // 清除搜索关键词，避免冲突
  if (categoryCode) {
    searchKeyword.value = ''
  }
}

// 方法
const handleSearch = () => {
  // 搜索时清除选择和快速过滤
  selectedContract.value = null
  if (searchKeyword.value.trim()) {
    quickFilter.value = ''
  }
}

const selectContract = (contract: ContractInfo) => {
  selectedContract.value = contract
  openContractPanel(contract)
}

const openContractPanel = async (contract: ContractInfo | null) => {
  if (!contract) {
    message.warning('请先选择一个合约')
    return
  }

  console.log('打开合约面板:', contract)
  
  try {
    // 设置当前选中的合约
    setCurrentContract(contract)
    
    // 发送合约选择事件到主窗口
    await emit('contract-selected', contract)
    
    // 关闭搜索窗口
    closeWindow()
    
    message.success(`正在打开 ${contract.name} 合约面板`)
  } catch (error) {
    console.error('打开合约面板失败:', error)
    message.error('打开合约面板失败')
  }
}

const closeWindow = async () => {
  try {
    const currentWindow = getCurrentWindow()
    await currentWindow.close()
  } catch (error) {
    console.error('关闭窗口失败:', error)
  }
}

// 更新价格数据
const updatePrices = async () => {
  try {
    await contractService.updateContractPrices()
    allCategories.value = contractService.getAllCategoriesSync()
  } catch (error) {
    console.error('更新价格数据失败:', error)
  }
}

// 初始化合约数据
const initializeContractData = async () => {
  try {
    isLoading.value = true
    console.log('🔍 初始化合约数据...')

    allCategories.value = await contractService.getAllCategories()

    // 计算总合约数
    totalContractsCount.value = allCategories.value.reduce((total, category) => {
      return total + category.contracts.length
    }, 0)

    console.log('✅ 合约数据初始化完成')
    console.log(`📊 共加载 ${totalContractsCount.value} 个合约，${allCategories.value.length} 个分类`)
  } catch (error) {
    console.error('❌ 初始化合约数据失败:', error)

    // 检查是否是连接问题
    const errorMessage = error instanceof Error ? error.message : String(error)
    if (errorMessage.includes('未连接') || errorMessage.includes('登录')) {
      showReconnectDialog.value = true
    } else {
      message.error('获取合约数据失败，请检查 CTP 连接状态')
    }
    allCategories.value = []
    totalContractsCount.value = 0
  } finally {
    isLoading.value = false
  }
}

// 处理重连成功
const handleReconnected = async () => {
  console.log('🔄 重连成功，重新初始化合约数据...')
  await initializeContractData()
}

// 生命周期
onMounted(async () => {
  // 初始化数据
  await initializeContractData()

  updatePrices();
  // 监听窗口失去焦点事件，失去焦点时关闭窗口
  const currentWindow = getCurrentWindow()

  // 监听窗口失去焦点
  const unlistenBlur = await currentWindow.listen('tauri://blur', () => {
    console.log('搜索窗口失去焦点，准备关闭')
    
  })

  // 存储取消监听函数，在组件卸载时清理
  ;(window as any).__unlistenBlur = unlistenBlur
})

onUnmounted(() => {
  // 清理窗口事件监听器
  const unlistenBlur = (window as any).__unlistenBlur
  if (unlistenBlur && typeof unlistenBlur === 'function') {
    unlistenBlur()
    delete (window as any).__unlistenBlur
  }
})
</script>

<style scoped>
.contract-search-window {
  padding: 16px;
  background-color: #0d1117;
  color: #c9d1d9;
  height: 100vh;
  display: flex;
  flex-direction: column;
  
  .search-container {
    margin-bottom: 16px;

    .search-input {
      border-radius: 6px;
      background-color: #161b22;
      border-color: #30363d;
      color: #c9d1d9;

      :deep(.ant-input) {
        background-color: #161b22;
        color: #c9d1d9;
      }

      :deep(.ant-input-prefix) {
        color: #8b949e;
      }
    }

    .search-stats {
      margin-top: 8px;
      display: flex;
      justify-content: space-between;
      align-items: center;

      .quick-filters {
        display: flex;
        align-items: center;
        gap: 8px;

        .filter-label {
          font-size: 12px;
          color: #8b949e;
          margin-right: 4px;
        }

        .filter-tag {
          padding: 2px 8px;
          font-size: 11px;
          background: #21262d;
          border: 1px solid #30363d;
          border-radius: 12px;
          color: #8b949e;
          cursor: pointer;
          transition: all 0.2s;
          user-select: none;

          &:hover {
            background: #30363d;
            color: #c9d1d9;
          }

          &.active {
            background: #1f6feb;
            border-color: #1f6feb;
            color: #ffffff;
          }
        }
      }

      .stats-info {
        .stats-text {
          font-size: 12px;
          color: #8b949e;

          &.loading {
            color: #1890ff;
          }
        }
      }
    }
  }

  .contract-list-container {
    flex: 1;
    border: 1px solid #30363d;
    border-radius: 6px;
    overflow: hidden;
    display: flex;
    flex-direction: column;

    .contract-header {
      display: flex;
      background: #161b22;
      border-bottom: 1px solid #30363d;
      padding: 12px 16px;
      font-weight: 600;
      color: #c9d1d9;

      .header-item {
        &.contract-code { width: 50%; }
        &.contract-name { width: 50%; }
        
        &.sortable {
          cursor: pointer;
          user-select: none;
          transition: all 0.2s;
          position: relative;

          &:hover {
            background: #21262d;
            color: #58a6ff;
          }

          &.active {
            color: #58a6ff;
          }

          .sort-icon {
            margin-left: 4px;
            font-size: 12px;
            opacity: 0.8;
          }
        }
      }
    }

    .contract-content {
      flex: 1;
      overflow-y: auto;
      background-color: #0d1117;

      .contract-category {
        .category-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 8px 16px;
          background: #161b22;
          border-bottom: 1px solid #30363d;
          font-weight: 500;
          color: #8b949e;

          .category-name {
            font-size: 14px;
          }

          .category-count {
            font-size: 12px;
            color: #8b949e;
          }
        }

        .category-contracts {
          .contract-item {
            display: flex;
            align-items: center;
            padding: 8px 16px;
            border-bottom: 1px solid #21262d;
            cursor: pointer;
            transition: all 0.2s;

            &:hover {
              background: #161b22;
            }

            &.selected {
              background: #1f6feb33;
              border-color: #1f6feb;
            }

            .contract-checkbox {
              width: 20px;
              margin-right: 8px;
            }

            .contract-code {
              width: 50%;
              font-family: 'Courier New', monospace;
              font-weight: 500;
              color: #c9d1d9;
            }

            .contract-name {
              width: 50%;
              color: #c9d1d9;
            }

          }
        }
      }

      .no-results {
        display: flex;
        justify-content: center;
        align-items: center;
        height: 200px;
        color: #8b949e;
      }
    }
  }

  .dialog-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 16px;
    padding-top: 16px;
    border-top: 1px solid #30363d;

    .footer-info {
      color: #8b949e;
      font-size: 14px;
    }

    .footer-actions {
      display: flex;
      gap: 8px;
    }
  }
}
</style>
