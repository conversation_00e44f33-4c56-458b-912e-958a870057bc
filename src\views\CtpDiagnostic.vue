<template>
  <div class="ctp-diagnostic">
    <div class="diagnostic-header">
      <h2>CTP 连接诊断</h2>
      <p>检查 CTP 连接状态和合约数据获取问题</p>
    </div>

    <div class="diagnostic-actions">
      <a-button 
        type="primary" 
        @click="runDiagnostic" 
        :loading="isRunning"
        size="large"
      >
        {{ isRunning ? '诊断中...' : '开始诊断' }}
      </a-button>
      
      <a-button 
        @click="clearResults" 
        :disabled="results.length === 0"
        style="margin-left: 12px;"
      >
        清除结果
      </a-button>
      
      <a-button 
        @click="copyReport" 
        :disabled="results.length === 0"
        style="margin-left: 12px;"
      >
        复制报告
      </a-button>
    </div>

    <!-- 诊断摘要 -->
    <div v-if="summary" class="diagnostic-summary">
      <a-card title="诊断摘要" size="small">
        <div class="summary-stats">
          <div class="stat-item success">
            <span class="stat-label">成功</span>
            <span class="stat-value">{{ summary.success }}</span>
          </div>
          <div class="stat-item error">
            <span class="stat-label">错误</span>
            <span class="stat-value">{{ summary.error }}</span>
          </div>
          <div class="stat-item warning">
            <span class="stat-label">警告</span>
            <span class="stat-value">{{ summary.warning }}</span>
          </div>
          <div class="stat-item total">
            <span class="stat-label">总计</span>
            <span class="stat-value">{{ summary.total }}</span>
          </div>
        </div>
      </a-card>
    </div>

    <!-- 诊断结果 -->
    <div v-if="results.length > 0" class="diagnostic-results">
      <a-card title="诊断结果" size="small">
        <div class="result-list">
          <div 
            v-for="(result, index) in results" 
            :key="index"
            :class="['result-item', result.status]"
          >
            <div class="result-header">
              <span class="result-icon">
                {{ getStatusIcon(result.status) }}
              </span>
              <span class="result-step">{{ result.step }}</span>
              <span class="result-status">{{ getStatusText(result.status) }}</span>
            </div>
            <div class="result-message">{{ result.message }}</div>
            <div v-if="result.details" class="result-details">
              <a-collapse size="small">
                <a-collapse-panel key="1" header="详细信息">
                  <pre>{{ JSON.stringify(result.details, null, 2) }}</pre>
                </a-collapse-panel>
              </a-collapse>
            </div>
          </div>
        </div>
      </a-card>
    </div>

    <!-- 建议解决方案 -->
    <div v-if="suggestions.length > 0" class="diagnostic-suggestions">
      <a-card title="建议解决方案" size="small">
        <div class="suggestion-list">
          <div 
            v-for="(suggestion, index) in suggestions" 
            :key="index"
            class="suggestion-item"
          >
            <div class="suggestion-title">{{ suggestion.title }}</div>
            <div class="suggestion-content">{{ suggestion.content }}</div>
            <div v-if="suggestion.action" class="suggestion-action">
              <a-button 
                type="link" 
                @click="suggestion.action"
                size="small"
              >
                {{ suggestion.actionText }}
              </a-button>
            </div>
          </div>
        </div>
      </a-card>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { message } from 'ant-design-vue'
import { ctpDiagnostic, type DiagnosticResult } from '@/utils/ctpDiagnostic'
import { ctpService } from '@/services/ctpService'
import { autoReconnectService } from '@/services/autoReconnectService'

// 响应式数据
const isRunning = ref(false)
const results = ref<DiagnosticResult[]>([])

// 计算属性
const summary = computed(() => {
  if (results.value.length === 0) return null
  return ctpDiagnostic.getSummary()
})

const suggestions = computed(() => {
  const errorResults = results.value.filter(r => r.status === 'error')
  const suggestions: Array<{
    title: string
    content: string
    action?: () => void
    actionText?: string
  }> = []

  // 根据错误类型生成建议
  errorResults.forEach(result => {
    switch (result.step) {
      case '用户信息检查':
        suggestions.push({
          title: '用户信息问题',
          content: '请检查登录信息是否正确，重新登录系统',
          action: () => {
            // 跳转到登录页面
            window.location.href = '/#/login'
          },
          actionText: '重新登录'
        })
        break
      
      case '交易连接检查':
        suggestions.push({
          title: '交易连接问题',
          content: '交易 API 连接失败，尝试重新连接',
          action: async () => {
            try {
              message.info('正在重新连接...')
              await autoReconnectService.ensureTraderConnection()
              message.success('重连成功')
              runDiagnostic()
            } catch (error) {
              message.error('重连失败')
            }
          },
          actionText: '重新连接'
        })
        break
      
      case '合约查询测试':
        suggestions.push({
          title: '合约查询问题',
          content: '合约数据获取失败，可能是网络问题或服务器问题',
          action: () => {
            runDiagnostic()
          },
          actionText: '重新测试'
        })
        break
    }
  })

  return suggestions
})

// 方法
const runDiagnostic = async () => {
  isRunning.value = true
  try {
    message.info('开始 CTP 连接诊断...')
    const diagnosticResults = await ctpDiagnostic.runFullDiagnostic()
    results.value = diagnosticResults
    
    const summary = ctpDiagnostic.getSummary()
    if (summary.error > 0) {
      message.error(`诊断完成，发现 ${summary.error} 个错误`)
    } else if (summary.warning > 0) {
      message.warning(`诊断完成，发现 ${summary.warning} 个警告`)
    } else {
      message.success('诊断完成，所有检查项正常')
    }
  } catch (error) {
    console.error('诊断失败:', error)
    message.error('诊断过程中发生错误')
  } finally {
    isRunning.value = false
  }
}

const clearResults = () => {
  results.value = []
  message.info('已清除诊断结果')
}

const copyReport = async () => {
  try {
    const report = ctpDiagnostic.generateReport()
    await navigator.clipboard.writeText(report)
    message.success('诊断报告已复制到剪贴板')
  } catch (error) {
    console.error('复制失败:', error)
    message.error('复制失败')
  }
}

const getStatusIcon = (status: string): string => {
  switch (status) {
    case 'success': return '✅'
    case 'error': return '❌'
    case 'warning': return '⚠️'
    default: return '❓'
  }
}

const getStatusText = (status: string): string => {
  switch (status) {
    case 'success': return '成功'
    case 'error': return '错误'
    case 'warning': return '警告'
    default: return '未知'
  }
}
</script>

<style scoped>
.ctp-diagnostic {
  padding: 24px;
  max-width: 1200px;
  margin: 0 auto;
}

.diagnostic-header {
  text-align: center;
  margin-bottom: 24px;
}

.diagnostic-header h2 {
  margin: 0 0 8px 0;
  color: #1890ff;
}

.diagnostic-header p {
  margin: 0;
  color: #666;
}

.diagnostic-actions {
  text-align: center;
  margin-bottom: 24px;
}

.diagnostic-summary {
  margin-bottom: 24px;
}

.summary-stats {
  display: flex;
  gap: 16px;
  justify-content: center;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 12px;
  border-radius: 6px;
  min-width: 80px;
}

.stat-item.success {
  background-color: #f6ffed;
  border: 1px solid #b7eb8f;
}

.stat-item.error {
  background-color: #fff2f0;
  border: 1px solid #ffccc7;
}

.stat-item.warning {
  background-color: #fffbe6;
  border: 1px solid #ffe58f;
}

.stat-item.total {
  background-color: #f0f0f0;
  border: 1px solid #d9d9d9;
}

.stat-label {
  font-size: 12px;
  color: #666;
  margin-bottom: 4px;
}

.stat-value {
  font-size: 20px;
  font-weight: bold;
}

.diagnostic-results {
  margin-bottom: 24px;
}

.result-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.result-item {
  padding: 16px;
  border-radius: 6px;
  border: 1px solid #d9d9d9;
}

.result-item.success {
  background-color: #f6ffed;
  border-color: #b7eb8f;
}

.result-item.error {
  background-color: #fff2f0;
  border-color: #ffccc7;
}

.result-item.warning {
  background-color: #fffbe6;
  border-color: #ffe58f;
}

.result-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
}

.result-icon {
  font-size: 16px;
}

.result-step {
  font-weight: bold;
  flex: 1;
}

.result-status {
  font-size: 12px;
  padding: 2px 8px;
  border-radius: 4px;
  background-color: rgba(0, 0, 0, 0.1);
}

.result-message {
  color: #333;
  margin-bottom: 8px;
}

.result-details {
  margin-top: 8px;
}

.result-details pre {
  background-color: #f5f5f5;
  padding: 8px;
  border-radius: 4px;
  font-size: 12px;
  overflow-x: auto;
}

.diagnostic-suggestions {
  margin-bottom: 24px;
}

.suggestion-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.suggestion-item {
  padding: 16px;
  background-color: #f0f8ff;
  border: 1px solid #91d5ff;
  border-radius: 6px;
}

.suggestion-title {
  font-weight: bold;
  color: #1890ff;
  margin-bottom: 8px;
}

.suggestion-content {
  color: #333;
  margin-bottom: 8px;
}

.suggestion-action {
  text-align: right;
}
</style>
